services:
  gateway:
    image: library/nginx:alpine
    ports:
      - 80:80
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: on-failure

  backend:
    image: localhost/backend:latest
    environment:
      - DB_HOST=database
      - DB_PORT=5432
      - DB_NAME=policy
      - DB_USERNAME=root
      - DB_PASSWORD=92469g70-234f-1235-a1D4-314b27Hj29be
    env_file:
      - backend.env
    ports:
      - 8000:8000
    restart: on-failure

  frontend:
    image: localhost/frontend:latest
    environment:
      - NEXT_PUBLIC_BASE_URL="/api/v1"
    ports:
      - 3000:3000
    restart: on-failure

  database:
    image: pgvector/pgvector:pg17
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: 92469g70-234f-1235-a1D4-314b27Hj29be
      POSTGRES_DB: policy
    ports:
      - 5432:5432
    volumes:
      - ./deploy/data:/var/lib/postgresql/data
    restart: on-failure
