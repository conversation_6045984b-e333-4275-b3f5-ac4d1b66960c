'use client'

import { createContext, ReactNode, useCallback, useContext, useMemo, useState } from 'react'
import { UseMutateAsyncFunction, useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { customAlphabet } from 'nanoid'

type ID = string

export type ChatThread = {
  id: ID
  title: string
}

type ThreadListContextType = {
  threads: ChatThread[]
  isPending: boolean
  isError: boolean
  currentThreadID: ID
  currentThread: ChatThread | undefined
  newThreadID: ID
  switchToThread: (thread: ID) => void
  switchToNewThread: () => void
  resetNewThreadID: () => void
  deleteThread: UseMutateAsyncFunction<void, Error, string>
  renameThread: UseMutateAsyncFunction<void, Error, { id: string; name: string }>
}

const ThreadListContext = createContext<ThreadListContextType | null>(null)

const generateId = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz')

export function ThreadListProvider({ children }: { children: ReactNode }) {
  const {
    data: threads = [],
    isPending,
    isError,
  } = useQuery({
    queryKey: ['threads'],
    queryFn: (): ChatThread[] => JSON.parse(localStorage.getItem('threads') || '[]'),
  })

  const queryClient = useQueryClient()

  const { mutateAsync: deleteThread } = useMutation({
    mutationFn: async (id: string) => {
      const currentValue = JSON.parse(localStorage.getItem('threads') || '[]') as ChatThread[]
      const newValue = currentValue.filter((thread) => thread.id !== id)
      localStorage.setItem('threads', JSON.stringify(newValue))
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['threads'] })
    },
  })

  const { mutateAsync: renameThread } = useMutation({
    mutationFn: async ({ id, name }: { id: string; name: string }) => {
      const currentValue = JSON.parse(localStorage.getItem('threads') || '[]') as ChatThread[]
      const newValue = currentValue.map((thread) => (thread.id === id ? { ...thread, title: name } : thread))
      localStorage.setItem('threads', JSON.stringify(newValue))
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['threads'] })
    },
  })

  const [newThreadID, setNewThreadID] = useState<ID>(generateId())
  const [currentThreadID, setCurrentThreadID] = useState<ID>(newThreadID)

  const currentThread = useMemo(() => threads.find((t) => t.id === currentThreadID), [currentThreadID, threads])

  const switchToThread = useCallback(
    (thread: ID) => {
      if (!threads.some((t) => t.id === thread)) {
        setCurrentThreadID(newThreadID)
      }
      setCurrentThreadID(thread)
    },
    [newThreadID, threads],
  )

  const switchToNewThread = useCallback(() => {
    setCurrentThreadID(newThreadID)
  }, [newThreadID])

  const resetNewThreadID = useCallback(() => {
    setNewThreadID(generateId())
  }, [])

  return (
    <ThreadListContext
      value={{
        threads,
        isPending,
        isError,
        currentThreadID,
        newThreadID,
        switchToThread,
        switchToNewThread,
        resetNewThreadID,
        currentThread,
        deleteThread,
        renameThread,
      }}
    >
      {children}
    </ThreadListContext>
  )
}

export function useThreadList() {
  const context = useContext(ThreadListContext)
  if (!context) {
    throw new Error('useThreadList must be used within a ThreadListProvider')
  }
  return context
}
