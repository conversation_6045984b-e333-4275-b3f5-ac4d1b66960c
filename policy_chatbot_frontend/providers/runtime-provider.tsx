'use client'

import { ReactNode } from 'react'
import { Assistant<PERSON>unt<PERSON>Provider as RuntimeProvider } from '@assistant-ui/react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

import { createFeedbackAdapter } from '@/lib/feedback-adapter'
import { createThreadHistoryAdapter } from '@/lib/thread-history-adapter'
import { ChatThread, useThreadList } from '@/providers/thread-list-provider'

export function AssistantRuntimeProvider({ children }: { children: ReactNode }) {
  const queryClient = useQueryClient()
  const { currentThreadID, newThreadID, resetNewThreadID } = useThreadList()

  const runtime = useChatRuntime({
    api: `${process.env.NEXT_PUBLIC_BASE_URL}/chat`,
    sendExtraMessageFields: true,
    onError: (error: string | Error) => {
      toast.error(typeof error === 'string' ? error : error.message)
    },
    headers:
      typeof window !== 'undefined'
        ? {
            Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
          }
        : undefined,
    body: {
      thread_id: currentThreadID,
    },
    adapters: {
      history: createThreadHistoryAdapter({
        threadId: currentThreadID,
        onNewMessage: (item) => {
          if (item.parentId === null) {
            resetNewThreadID()
            const newThread = {
              id: newThreadID,
              title: item.message.content?.[0].type === 'text' ? item.message.content?.[0].text : '新对话',
            }
            const currentThreads: ChatThread[] = JSON.parse(localStorage.getItem('threads') || '[]')
            localStorage.setItem('threads', JSON.stringify([newThread, ...currentThreads]))
            queryClient.setQueryData(['threads'], (prev: ChatThread[]) => [newThread, ...prev])
          }
        },
      }),
      feedback: createFeedbackAdapter(),
    },
  })

  return <RuntimeProvider runtime={runtime}>{children}</RuntimeProvider>
}
