import { z } from 'zod'

export type PermissionRole = number | null

export const DocumentUpsertSchema = z.object({
  source: z.string().min(1, '来源不能为空'),
  version: z.string().min(1, '版本不能为空'),
  available: z.boolean(),
  permission_role: z.number().int().nullable(),
  mark_as_qa: z.boolean(),
  qa_description: z.string(),
})

export type DocumentUpsertDto = z.infer<typeof DocumentUpsertSchema>

export interface DocumentDto {
  document_id: number
  source: string
  version: string
  available: boolean
  permission_role: PermissionRole
  created_at: string
  updated_at: string
  mark_as_qa: boolean
  qa_description: string | null
}

export interface Document {
  id: number
  source: string
  version: string
  available: boolean
  permissionRole: number | null
  createdAt: Date
  updatedAt: Date
  isQADocument: boolean
  qaDescription: string
}

export interface PagedDocumentDto {
  total: number
  documents: DocumentDto[]
}

export interface PagedChunkDto {
  total: number
  chunks: ChunkDto[]
}

export interface ChunkDto {
  chunk_id: number
  content: string
  document: number
  created_at: string
  updated_at: string
}

export interface Chunk {
  id: number
  content: string
  documentId: number
  createdAt: Date
  updatedAt: Date
}

export function toDocument(dto: DocumentDto): Document {
  return {
    id: dto.document_id,
    source: dto.source.trim(),
    version: dto.version.trim(),
    available: dto.available,
    permissionRole: dto.permission_role,
    createdAt: new Date(dto.created_at),
    updatedAt: new Date(dto.updated_at),
    isQADocument: dto.mark_as_qa,
    qaDescription: dto.qa_description ?? '',
  }
}

export function toChunk(dto: ChunkDto): Chunk {
  return {
    id: dto.chunk_id,
    content: dto.content,
    documentId: dto.document,
    createdAt: new Date(dto.created_at),
    updatedAt: new Date(dto.updated_at),
  }
}

export function toDocumentUpsertDto(doc: Document): DocumentUpsertDto {
  return {
    source: doc.source,
    version: doc.version,
    available: doc.available,
    permission_role: doc.permissionRole,
    mark_as_qa: doc.isQADocument,
    qa_description: doc.qaDescription,
  }
}
