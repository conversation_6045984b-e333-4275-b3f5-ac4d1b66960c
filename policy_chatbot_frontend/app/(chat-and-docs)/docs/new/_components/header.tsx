'use client'

import { useRouter } from 'next/navigation'

import { AppBackButton } from '@/components/app-ui/app-back-button'
import { AppHeader } from '@/components/app-ui/app-header'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

export function Header() {
  const { back } = useRouter()

  return (
    <AppHeader>
      <AppBackButton />
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem className="hidden md:block">
            <BreadcrumbLink onClick={back} asChild>
              <button>文档列表</button>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>添加文档</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </AppHeader>
  )
}
