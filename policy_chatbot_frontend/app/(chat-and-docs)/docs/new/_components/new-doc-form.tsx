'use client'

import { useRouter } from 'next/navigation'
import type { UseFormReturn } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { dateTimeToString } from '@/lib/date-time'
import { cn } from '@/lib/utils'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

import { DocContentEditor } from '../../_components/doc-content-editor'
import { DocPermissionInput } from '../../_components/input/doc-permission-input'
import { DocStatusInput } from '../../_components/input/doc-status-input'
import { DocTypeInput } from '../../_components/input/doc-type-input'
import { DocumentRepository } from '../../api'
import { Document, DocumentUpsertDto, DocumentUpsertSchema } from '../../dto'

export const docCreatorFormFieldsSchema = DocumentUpsertSchema.and(z.object({ content: z.string() }))
export type DocCreatorFormFields = z.infer<typeof docCreatorFormFieldsSchema>

export function NewDocForm({ form, id }: { form: UseFormReturn<DocCreatorFormFields>; id?: string }) {
  const router = useRouter()

  async function onSubmit(info: DocumentUpsertDto, content: string) {
    let doc: Document
    try {
      doc = await DocumentRepository.create(info)
    } catch (e) {
      toast.error(`文档创建失败：${e}`)
      return
    }
    if (content) {
      try {
        await DocumentRepository.importChunks(doc.id, content)
      } catch (e) {
        toast.error(`文档已创建，但内容导入失败：${e}`)
      }
    }
    router.push(`/docs/${doc.id}`)
  }

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) =>
            onSubmit(
              {
                ...data,
                available: false,
                qa_description: data.mark_as_qa ? data.qa_description : '',
              },
              data.content,
            ),
          )}
          className="space-y-6"
          id={id}
        >
          <section>
            <h2 className="mb-2 font-bold">基本信息</h2>
            <div className="bg-secondary/50 grid-cols-[auto_1fr] gap-4 rounded-lg p-4 not-sm:space-y-6 sm:grid sm:[&_[data-slot=form-label]]:justify-self-end sm:[&_[data-slot=form-label]]:text-nowrap sm:[&_[data-slot=form-message]]:col-start-2">
              <FormField
                control={form.control}
                name="source"
                render={({ field }) => (
                  <FormItem className="sm:contents">
                    <FormLabel>来源</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="version"
                render={({ field }) => (
                  <FormItem className="sm:contents">
                    <FormLabel>版本</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="permission_role"
                render={({ field }) => (
                  <FormItem className="sm:contents">
                    <FormLabel>权限等级</FormLabel>
                    <FormControl>
                      <DocPermissionInput {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="mark_as_qa"
                render={({ field }) => (
                  <FormItem className="sm:contents">
                    <FormLabel>文档类型</FormLabel>
                    <FormControl>
                      <DocTypeInput
                        value={field.value ? 'qa' : 'normal'}
                        onChange={(type) => field.onChange(type === 'qa')}
                        disabled={field.disabled}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="qa_description"
                disabled={!form.watch('mark_as_qa')}
                render={({ field }) => (
                  <FormItem className="sm:contents">
                    <FormLabel className={cn('mt-2.5 self-start', field.disabled && 'text-muted-foreground')}>
                      文档说明
                    </FormLabel>
                    <FormControl>
                      <Textarea placeholder="仅限问答文档" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </section>
          <section className="space-y-4">
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <h2>
                    <FormLabel className="text-base font-bold">文档内容</FormLabel>
                  </h2>
                  <FormControl>
                    <DocContentEditor
                      {...field}
                      onInfoChange={({ name, lastModified }) => {
                        if (!form.getValues('source')) {
                          form.setValue('source', name)
                        }
                        if (!form.getValues('version')) {
                          form.setValue('version', `${dateTimeToString(lastModified)}版`)
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>
        </form>
      </Form>
    </>
  )
}
