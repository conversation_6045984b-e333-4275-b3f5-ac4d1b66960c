'use client'

import { useId } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'

import { PendingButton } from '@/components/ui/button'
import { Card, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

import { Header } from './_components/header'
import { DocCreatorFormFields, docCreatorFormFieldsSchema, NewDocForm } from './_components/new-doc-form'

export default function Page() {
  const formId = useId()
  const form = useForm<DocCreatorFormFields>({
    defaultValues: {
      source: '',
      version: '',
      available: false,
      permission_role: 1,
      mark_as_qa: false,
      qa_description: '',
      content: '',
    },
    resolver: zodResolver(docCreatorFormFieldsSchema),
  })

  return (
    <>
      <Header />
      <main className="p-4">
        <Card className="mx-auto mt-4 max-w-3xl">
          <CardHeader>
            <CardTitle className="text-xl">
              <h1>添加文档</h1>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <NewDocForm id={formId} form={form} />
          </CardContent>
          <CardFooter className="justify-end gap-4">
            <p className="text-muted-foreground text-sm">文档创建后仍可修改</p>
            <PendingButton type="submit" form={formId} pending={form.formState.isSubmitting}>
              创建文档
            </PendingButton>
          </CardFooter>
        </Card>
      </main>
    </>
  )
}
