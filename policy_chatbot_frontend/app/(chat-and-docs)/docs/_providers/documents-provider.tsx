'use client'

import { createContext, ReactNode, useCallback, useContext } from 'react'
import { useAtom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'

import type { Document } from '../dto'

type DocumentsContextType = {
  recentDocuments: Document[]
  appendRecent: (document: Document) => void
  removeRecent: (id: number) => void
  clearRecent: () => void
}

const DocumentContext = createContext<DocumentsContextType | null>(null)

const recentDocumentsAtom = atomWithStorage<Document[]>('recent-documents', [])

export function DocumentsProvider({ children, limit = 15 }: { children: ReactNode; limit?: number }) {
  const [recentDocuments, setRecentDocuments] = useAtom<Document[]>(recentDocumentsAtom)

  const appendRecent = useCallback(
    (document: Document) => {
      setRecentDocuments((prev) => {
        const prevFind = prev.findIndex((d) => d.id === document.id)
        if (prevFind !== -1) {
          return prev
          // return [document, ...prev.slice(0, prevFind), ...prev.slice(prevFind + 1)]
        }
        if (prev.length >= limit) {
          return [document, ...prev.slice(0, limit - 1)]
        }
        return [document, ...prev]
      })
    },
    [limit, setRecentDocuments],
  )

  const removeRecent = useCallback(
    (id: number) => {
      setRecentDocuments((prev) => prev.filter((d) => d.id !== id))
    },
    [setRecentDocuments],
  )

  const clearRecent = useCallback(() => {
    setRecentDocuments([])
  }, [setRecentDocuments])

  return (
    <DocumentContext value={{ recentDocuments, appendRecent, removeRecent, clearRecent }}>{children}</DocumentContext>
  )
}

export function useDocuments() {
  const context = useContext(DocumentContext)
  if (!context) {
    throw new Error('useDocuments must be used within a DocumentsProvider')
  }
  return context
}
