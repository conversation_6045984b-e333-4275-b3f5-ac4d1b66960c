import { client } from '@/lib/request'

import {
  Chunk,
  ChunkDto,
  Document,
  DocumentDto,
  DocumentUpsertDto,
  PagedChunkDto,
  PagedDocumentDto,
  toChunk,
  toDocument,
} from './dto'

type ID = number | string

interface ListOptions {
  skip: number
  limit: number
}

export class DocumentRepository {
  static async list(opts: ListOptions): Promise<{ total: number; documents: Document[] }> {
    return client
      .get<PagedDocumentDto>('admin/documents', { searchParams: { ...opts } })
      .json()
      .then(({ total, documents }) => ({
        total,
        documents: documents.map(toDocument),
      }))
  }

  static async create(source: DocumentUpsertDto): Promise<Document> {
    return client.post<DocumentDto>('admin/documents', { json: source }).json().then(toDocument)
  }

  static async get(documentId: ID): Promise<Document> {
    return client.get<DocumentDto>(`admin/documents/${documentId}`).json().then(toDocument)
  }

  static async update(documentId: ID, source: DocumentUpsertDto): Promise<void> {
    await client.put(`admin/documents/${documentId}`, { json: source })
  }

  static async delete(documentId: ID): Promise<void> {
    await client.delete(`admin/documents/${documentId}`)
  }

  static async listChunks(documentId: ID, opts: ListOptions): Promise<{ total: number; chunks: Chunk[] }> {
    return client
      .get<PagedChunkDto>(`admin/documents/${documentId}/chunks`, { searchParams: { ...opts } })
      .json()
      .then(({ total, chunks }) => ({ total, chunks: chunks.map(toChunk) }))
  }

  static async importChunks(documentId: ID, content: string): Promise<{ total: number; chunks: Chunk[] }> {
    return client
      .post<PagedChunkDto>(`admin/documents/${documentId}/import`, {
        json: { content },
        timeout: 10 * 60 * 1000, // 涉及解析，可能比较慢
      })
      .json()
      .then(({ total, chunks }) => ({ total, chunks: chunks.map(toChunk) }))
  }

  static async createChunk(documentId: ID, content: string): Promise<Chunk> {
    return client.post<ChunkDto>(`admin/documents/${documentId}/chunks`, { json: { content } }).json().then(toChunk)
  }

  static async updateChunk(documentId: ID, chunkId: ID, content: string): Promise<void> {
    await client.put(`admin/documents/${documentId}/chunks/${chunkId}`, { json: { content } })
  }

  static async deleteChunk(documentId: ID, chunkId: ID): Promise<void> {
    await client.delete(`admin/documents/${documentId}/chunks/${chunkId}`)
  }
}

export async function parseDocument(file: File): Promise<string> {
  const formData = new FormData()
  formData.append('file', file)
  return await client
    .post<{ content: string }>('admin/documents/parse', {
      body: formData,
      timeout: 10 * 60 * 1000, // 涉及解析，可能比较慢
    })
    .json()
    .then(({ content }) => content)
}
