'use client'

import { useAtomValue } from 'jotai'

import { DateTime } from '@/components/date-time'
import { Badge } from '@/components/ui/badge'
import { Tooltip } from '@/components/ui/tooltip'

import type { Document } from '../../dto'
import { isEditingAtom } from '../atoms'
import { useCurrentDocInfo } from '../hooks'
import { DocInfoEditButton } from './doc-info-edit'

export function DocInfo() {
  const { data, isError, isLoading } = useCurrentDocInfo()
  if (isLoading) {
    return null
  }
  if (isError) {
    return <p>加载文档信息失败</p>
  }
  return <DocInfoRenderer document={data!} />
}

function DocInfoRenderer({ document: doc }: { document: Document }) {
  const isEditing = useAtomValue(isEditingAtom)

  return (
    <div className="bg-muted relative mx-auto flex max-w-5xl flex-col gap-2 rounded-md p-4">
      <div className="text-2xl">
        {doc.isQADocument && (
          <Tooltip
            tip="这是一篇问答文档"
            className="bg-background -my-0.5 mr-2 -ml-1 inline-flex h-7 translate-y-1 items-center justify-center rounded-sm border px-1 align-top text-sm font-semibold"
          >
            问答
          </Tooltip>
        )}
        <h1 className="inline font-bold">{doc.source}</h1>
        <span className="select-none"> </span>
        {doc.available ? (
          <Badge className="translate-y-1 border-none align-top">已启用</Badge>
        ) : (
          <Badge className="translate-y-1 border-none bg-yellow-700 align-top dark:bg-yellow-300">未启用</Badge>
        )}
      </div>
      <p className="text-muted-foreground text-sm">版本：{doc.version}</p>
      <p className="text-muted-foreground flex gap-6 text-sm">
        <span>
          创建于
          <DateTime datetime={doc.createdAt} precision="second" />
        </span>
        <span>
          修改于
          <DateTime datetime={doc.updatedAt} precision="second" />
        </span>
      </p>
      {doc.isQADocument && doc.qaDescription && <p>{doc.qaDescription}</p>}
      {isEditing && (
        <ul className="absolute top-2 right-2 flex items-center gap-2">
          <li>
            <DocInfoEditButton document={doc} />
          </li>
        </ul>
      )}
    </div>
  )
}
