'use client'

import { useId, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { PencilIcon } from 'lucide-react'
import { useForm, UseFormReturn } from 'react-hook-form'
import { toast } from 'sonner'

import { cn } from '@/lib/utils'
import { Button, PendingButton } from '@/components/ui/button'
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Tooltip } from '@/components/ui/tooltip'

import { DocPermissionInput } from '../../_components/input/doc-permission-input'
import { DocStatusInput } from '../../_components/input/doc-status-input'
import { DocTypeInput } from '../../_components/input/doc-type-input'
import { DocumentRepository } from '../../api'
import { DocumentUpsertSchema, toDocumentUpsertDto, type Document, type DocumentUpsertDto } from '../../dto'

export function DocInfoEditButton({ document }: { document: Document }) {
  const queryClient = useQueryClient()
  const [open, setOpen] = useState(false)
  const formId = useId()

  const form = useForm<DocumentUpsertDto>({
    defaultValues: toDocumentUpsertDto(document),
    resolver: zodResolver(DocumentUpsertSchema),
  })

  async function onSubmit(data: DocumentUpsertDto) {
    try {
      await DocumentRepository.update(document.id, data)
      await queryClient.invalidateQueries({
        queryKey: ['documents', document.id.toString(), 'info'],
      })
      toast.success('文档信息已修改')
      setOpen(false)
    } catch (e) {
      toast.error('文档信息修改失败', { description: String(e) })
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Tooltip tip="编辑文档信息" asChild>
        <DialogTrigger asChild>
          <Button variant="outline" size="icon-sm">
            <PencilIcon />
          </Button>
        </DialogTrigger>
      </Tooltip>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>编辑文档信息</DialogTitle>
        </DialogHeader>
        <DocInfoForm id={formId} form={form} onSubmit={onSubmit} />
        <DialogFooter>
          <PendingButton form={formId} type="submit" pending={form.formState.isSubmitting}>
            保存
          </PendingButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

function DocInfoForm({
  onSubmit,
  form,
  id,
}: {
  form: UseFormReturn<DocumentUpsertDto>
  onSubmit: (data: DocumentUpsertDto) => Promise<void>
  id?: string
}) {
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) =>
          onSubmit({ ...data, qa_description: data.mark_as_qa ? data.qa_description : '' }),
        )}
        className="flex flex-col gap-6"
        id={id}
      >
        <div className="contents grid-cols-[auto_1fr] gap-4 sm:grid sm:[&_[data-slot=form-label]]:justify-self-end sm:[&_[data-slot=form-label]]:text-nowrap sm:[&_[data-slot=form-message]]:col-start-2">
          <FormField
            control={form.control}
            name="source"
            render={({ field }) => (
              <FormItem className="sm:contents">
                <FormLabel>来源</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="version"
            render={({ field }) => (
              <FormItem className="sm:contents">
                <FormLabel>版本</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="available"
            render={({ field }) => (
              <FormItem className="sm:contents">
                <FormLabel>状态</FormLabel>
                <FormControl>
                  <DocStatusInput {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="permission_role"
            render={({ field }) => (
              <FormItem className="sm:contents">
                <FormLabel>权限等级</FormLabel>
                <FormControl>
                  <DocPermissionInput {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="mark_as_qa"
            render={({ field }) => (
              <FormItem className="sm:contents">
                <FormLabel>文档类型</FormLabel>
                <FormControl>
                  <DocTypeInput
                    value={field.value ? 'qa' : 'normal'}
                    onChange={(type) => field.onChange(type === 'qa')}
                    disabled={field.disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="qa_description"
            disabled={!form.watch('mark_as_qa')}
            render={({ field }) => (
              <FormItem className="sm:contents">
                <FormLabel className={cn('mt-2.5 self-start', field.disabled && 'text-muted-foreground')}>
                  文档说明
                </FormLabel>
                <FormControl>
                  <Textarea placeholder="仅限问答文档" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  )
}
