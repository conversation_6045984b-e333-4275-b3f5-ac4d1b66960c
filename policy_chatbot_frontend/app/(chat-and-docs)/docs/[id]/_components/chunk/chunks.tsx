'use client'

import { useEffect, useRef, useState } from 'react'
import { useParams } from 'next/navigation'
import { InfiniteData, useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useAtomValue } from 'jotai'
import { PencilIcon, Trash2Icon } from 'lucide-react'
import { toast } from 'sonner'

import { LoadError, LoadErrorDescription, LoadErrorRetryTrigger, LoadErrorTitle } from '@/components/app-ui/load-error'
import { Markdown } from '@/components/app-ui/markdown'
import { Button, PromiseButton } from '@/components/ui/button'
import { Skeleton, SkeletonText } from '@/components/ui/skeleton'
import { Tooltip } from '@/components/ui/tooltip'

import { DocumentRepository } from '../../../api'
import { type Chunk as ChunkType } from '../../../dto'
import { isEditingAtom } from '../../atoms'
import { ContentInitializer } from '../content-initializer'
import { NewChunkButton, UpdateChunkEditor } from './chunk-edit'

const PAGE_SIZE = 15

type Page = {
  total: number
  chunks: (ChunkType & { temp?: boolean })[]
}
type QueryData = InfiniteData<Page, number>

function deleteChunkFromData(data: QueryData, pageNumber: number, numberInPage: number): QueryData {
  const newTotal = data.pages.at(-1)?.total ?? 0
  const newPagesPart1 = data.pages.slice(0, pageNumber).map((p) => ({
    total: newTotal,
    chunks: p.chunks,
  }))
  const chunksInSamePageAndAfter = data.pages.slice(pageNumber).flatMap((page) => page.chunks)
  chunksInSamePageAndAfter.splice(numberInPage, 1)
  const newPagesPart2 = chunksInSamePageAndAfter.reduce<Page[]>((pages, chunk, i) => {
    if (i % PAGE_SIZE === 0) {
      return [...pages, { total: newTotal, chunks: [chunk] }]
    }
    return pages.with(-1, { total: newTotal, chunks: [...pages.at(-1)!.chunks, chunk] })
  }, [])

  const newPages = [...newPagesPart1, ...newPagesPart2]

  return {
    pages: newPages,
    pageParams: newPagesPart1.map((_, i) => i + 1),
  }
}

function updateChunkInData(data: QueryData, pageNumber: number, numberInPage: number, chunk: ChunkType): QueryData {
  const thePage = data.pages[pageNumber]
  return {
    pages: data.pages.with(pageNumber, {
      total: thePage.total,
      chunks: thePage.chunks.with(numberInPage, chunk),
    }),
    pageParams: data.pageParams,
  }
}

function createChunkInData(data: QueryData | undefined, chunk: ChunkType): QueryData {
  if (!data || data.pages.length === 0) {
    return {
      pages: [{ total: 1, chunks: [chunk] }],
      pageParams: [1],
    }
  }

  const lastPage = data.pages.at(-1)!
  const newTotal = lastPage.total + 1
  let newPages: Page[]

  if (lastPage.chunks.length < PAGE_SIZE) {
    newPages = [
      ...data.pages.slice(0, -1).map((x) => ({ ...x, total: newTotal })),
      { total: newTotal, chunks: [...lastPage.chunks, chunk] },
    ]
  } else {
    newPages = [...data.pages.map((x) => ({ ...x, total: newTotal })), { total: newTotal, chunks: [chunk] }]
  }

  return {
    pages: newPages,
    pageParams: newPages.map((_, i) => i + 1),
  }
}

export function ChunksList() {
  const { id: documentId } = useParams<{ id: string }>()

  const loadMoreRef = useRef<HTMLDivElement>(null)

  const isEditing = useAtomValue(isEditingAtom)
  const queryClient = useQueryClient()

  const { data, isPending, isError, error, fetchNextPage, isFetchingNextPage, hasNextPage, refetch } = useInfiniteQuery(
    {
      queryKey: ['documents', documentId, 'chunks'],
      queryFn: ({ pageParam }): Promise<Page> =>
        DocumentRepository.listChunks(documentId, { skip: pageParam, limit: PAGE_SIZE }),
      initialPageParam: 0,
      getNextPageParam: (lastPage, pages) => {
        if (pages.length * PAGE_SIZE >= lastPage.total) return undefined
        return pages.length * PAGE_SIZE
      },
    },
  )

  useEffect(() => {
    if (!hasNextPage || isFetchingNextPage) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          fetchNextPage()
        }
      },
      { threshold: 0.1 },
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => observer.disconnect()
  }, [fetchNextPage, hasNextPage, isFetchingNextPage])

  if (isPending) {
    return (
      <div className="mx-auto max-w-3xl">
        <ul className="flex flex-col">
          {Array.from({ length: 5 }, (_, index) => (
            <ChunkSkeleton key={index} />
          ))}
        </ul>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="mx-auto max-w-3xl">
        <LoadError>
          <LoadErrorTitle>获取文档时发生错误</LoadErrorTitle>
          <LoadErrorDescription>{error.message}</LoadErrorDescription>
          <LoadErrorRetryTrigger onClick={() => refetch()} />
        </LoadError>
      </div>
    )
  }

  if (data.pages.length === 0 || data.pages.every((page) => page.chunks.length === 0)) {
    return <ContentInitializer documentId={documentId} />
  }

  return (
    <div className="mx-auto max-w-3xl">
      <ul className="flex flex-col">
        {data.pages.map((page, pageNumber) =>
          page.chunks.map((chunk, numberInPage) => (
            <Chunk
              key={chunk.id}
              documentId={documentId}
              chunk={chunk}
              onMutation={(mutation) => {
                switch (mutation.type) {
                  case 'delete':
                    queryClient.setQueryData<QueryData>(
                      ['documents', documentId, 'chunks'],
                      (data) => data && deleteChunkFromData(data, pageNumber, numberInPage),
                    )
                    break
                  case 'update':
                    queryClient.setQueryData<QueryData>(
                      ['documents', documentId, 'chunks'],
                      (data) => data && updateChunkInData(data, pageNumber, numberInPage, mutation.chunk),
                    )
                    break
                }
              }}
            />
          )),
        )}
        {isEditing && (
          <li>
            <NewChunkButton
              documentId={documentId}
              onSuccess={(chunk) => {
                queryClient.setQueryData<QueryData>(['documents', documentId, 'chunks'], (data) =>
                  createChunkInData(data, chunk),
                )
              }}
            />
          </li>
        )}
      </ul>
      {hasNextPage && (
        <div ref={loadMoreRef} className="mt-6 h-8">
          <p className="text-muted-foreground text-center text-sm">{isFetchingNextPage ? '正在加载…' : '待加载'}</p>
        </div>
      )}
    </div>
  )
}

type ChunkMutation =
  | {
      type: 'delete'
    }
  | {
      type: 'update'
      chunk: ChunkType
    }

function Chunk({
  documentId,
  chunk,
  onMutation,
}: {
  documentId: string
  chunk: ChunkType
  onMutation: (mutation: ChunkMutation) => void
}) {
  const isEditingMode = useAtomValue(isEditingAtom)
  const [deleting, setDeleting] = useState(false)
  const [contentEditing, setContentEditing] = useState(false)

  return (
    <li className="relative py-4 not-first:border-t">
      {isEditingMode && contentEditing ? (
        <UpdateChunkEditor
          chunk={chunk}
          onCancel={() => {
            setContentEditing(false)
          }}
          onSuccess={(chunk) => {
            setContentEditing(false)
            onMutation({ type: 'update', chunk })
          }}
          autoFocus
        />
      ) : (
        <>
          <Markdown
            content={chunk.content || '（空内容块）'}
            className={deleting || !chunk.content ? 'opacity-50' : undefined}
          />
          {isEditingMode && (
            <>
              <p className="sr-only">内容块操作</p>
              <ul className="absolute top-2 right-0 flex items-center gap-2">
                <li>
                  <Tooltip tip="编辑内容块" asChild>
                    <Button
                      size="icon-sm"
                      variant="outline"
                      onClick={() => setContentEditing(true)}
                      disabled={deleting}
                    >
                      <PencilIcon />
                    </Button>
                  </Tooltip>
                </li>
                <li>
                  <Tooltip tip="删除内容块" asChild>
                    <PromiseButton
                      size="icon-sm"
                      variant="outline"
                      icon={<Trash2Icon />}
                      onClick={async () => {
                        setDeleting(true)
                        try {
                          await DocumentRepository.deleteChunk(documentId, chunk.id)
                          toast.success('内容块已删除')
                          onMutation({ type: 'delete' })
                        } catch (e) {
                          toast.error(`删除内容块失败：${e}`)
                        }
                      }}
                    />
                  </Tooltip>
                </li>
              </ul>
            </>
          )}
        </>
      )}
    </li>
  )
}

function ChunkSkeleton() {
  return (
    <li className="py-4 not-last:border-b">
      <SkeletonText size="base">
        <Skeleton className="w-full" />
      </SkeletonText>
      <SkeletonText size="base">
        <Skeleton className="w-full" />
      </SkeletonText>
      <SkeletonText size="base">
        <Skeleton className="w-full" />
      </SkeletonText>
      <SkeletonText size="base">
        <Skeleton className="w-1/2" />
      </SkeletonText>
    </li>
  )
}
