'use client'

import { useId, useState } from 'react'
import { PlusCircleIcon } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

import { But<PERSON>, PendingButton } from '@/components/ui/button'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'

import { DocumentRepository } from '../../../api'
import { type Chunk as ChunkType } from '../../../dto'

export function NewChunkButton({
  documentId,
  onSuccess,
}: {
  documentId: string
  onSuccess: (chunk: ChunkType) => void
}) {
  const line = (
    <span className="bg-primary h-0.5 flex-[0_1_12rem] opacity-80 transition-[opacity,flex-grow] group-hover:grow group-hover:opacity-100 group-focus-visible:grow group-focus-visible:opacity-100" />
  )
  const [editing, setEditing] = useState(false)

  if (!editing) {
    return (
      <button
        className="group text-primary mb-4 flex w-full items-center justify-center gap-2 hover:gap-4 focus-visible:gap-4"
        type="button"
        onClick={() => setEditing(true)}
      >
        {line}
        <span className="group-hover:bg-primary group-hover:text-primary-foreground group-focus-visible:bg-primary group-focus-visible:text-primary-foreground flex shrink-0 items-center gap-1 rounded-md px-2 py-1 transition-[padding,color,background-color] group-hover:px-8 group-focus-visible:px-8">
          <PlusCircleIcon className="size-4" />
          新建内容块
        </span>
        {line}
      </button>
    )
  }
  return (
    <NewChunkEditor
      documentId={documentId}
      onCancel={() => setEditing(false)}
      onSuccess={(chunk) => {
        setEditing(false)
        onSuccess(chunk)
      }}
      autoFocus
    />
  )
}

function ChunkEditor({
  title,
  defaultValues = { content: '' },
  onCancel,
  onSubmit,
  className,
  autoFocus,
}: {
  title: React.ReactNode
  defaultValues?: { content: string }
  onCancel?: () => void
  onSubmit: (data: { content: string }) => Promise<void>
  className?: string
  autoFocus?: boolean
}) {
  const formId = useId()

  const form = useForm<{ content: string }>({ defaultValues })

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form id={formId} onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="content"
              rules={{ required: '内容不能为空' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">内容</FormLabel>
                  <FormControl>
                    <Textarea {...field} autoFocus={autoFocus} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
      <CardFooter className="justify-between">
        {onCancel && (
          <Button variant="outline" size="sm" onClick={onCancel}>
            取消
          </Button>
        )}
        <PendingButton size="sm" type="submit" form={formId} pending={form.formState.isSubmitting}>
          保存
        </PendingButton>
      </CardFooter>
    </Card>
  )
}

export function UpdateChunkEditor({
  chunk,
  onCancel,
  onSuccess,
  className,
  autoFocus,
}: {
  chunk: ChunkType
  onCancel: () => void
  onSuccess: (chunk: ChunkType) => void
  className?: string
  autoFocus?: boolean
}) {
  async function onSubmit(data: { content: string }) {
    try {
      await DocumentRepository.updateChunk(chunk.documentId, chunk.id, data.content)
    } catch (e) {
      toast.error('内容块更新失败', { description: String(e) })
      return
    }
    toast.success('内容块已更新')
    onSuccess({
      ...chunk,
      content: data.content,
      updatedAt: new Date(),
    })
  }

  return (
    <ChunkEditor
      title="编辑内容块"
      defaultValues={{ content: chunk.content }}
      onSubmit={onSubmit}
      onCancel={onCancel}
      className={className}
      autoFocus={autoFocus}
    />
  )
}

export function NewChunkEditor({
  documentId,
  onCancel,
  onSuccess,
  title = '新建内容快',
  className,
  autoFocus,
}: {
  documentId: string
  onCancel?: () => void
  onSuccess: (chunk: ChunkType) => void
  title?: React.ReactNode
  className?: string
  autoFocus?: boolean
}) {
  async function onSubmit(data: { content: string }) {
    try {
      const chunk = await DocumentRepository.createChunk(documentId, data.content)
      toast.success('内容块已创建')
      onSuccess(chunk)
    } catch (e) {
      toast.error('内容块创建失败', { description: String(e) })
    }
  }

  return (
    <ChunkEditor title={title} onSubmit={onSubmit} onCancel={onCancel} className={className} autoFocus={autoFocus} />
  )
}
