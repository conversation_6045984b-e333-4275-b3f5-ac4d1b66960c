'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { useAtom } from 'jotai'
import { LogOutIcon, PencilIcon } from 'lucide-react'

import { AppBackButton } from '@/components/app-ui/app-back-button'
import { AppHeader } from '@/components/app-ui/app-header'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Skeleton, SkeletonText } from '@/components/ui/skeleton'

import { useDocuments } from '../../_providers/documents-provider'
import { isEditingAtom } from '../atoms'
import { useCurrentDocInfo } from '../hooks'

export function DocHeader() {
  const from = useSearchParams().get('from')
  const fromPage = from?.match(/^page-([1-9]\d*)$/)?.[1]

  const { appendRecent } = useDocuments()
  const { data, isPending } = useCurrentDocInfo()

  useEffect(() => {
    if (typeof window === 'undefined' || !data) return
    appendRecent(data)
  }, [data, appendRecent])

  return (
    <AppHeader className="z-1">
      <AppBackButton />
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem className="hidden md:block">
            <BreadcrumbLink asChild>
              <Link href={fromPage && fromPage !== '1' ? `/docs?page=${fromPage}` : '/docs'}>文档列表</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>
              {isPending ? (
                <SkeletonText size="sm">
                  <Skeleton className="w-32" />
                </SkeletonText>
              ) : (
                data?.source || '文档详情'
              )}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <ul className="flex flex-grow justify-end gap-[inherit]">
        <li>
          <EditButton />
        </li>
      </ul>
    </AppHeader>
  )
}

function EditButton() {
  const [isEditing, setIsEditing] = useAtom(isEditingAtom)
  return (
    <Button size="sm" onClick={() => setIsEditing((prev) => !prev)}>
      {isEditing ? (
        <>
          <LogOutIcon />
          退出编辑模式
        </>
      ) : (
        <>
          <PencilIcon />
          编辑文档
        </>
      )}
    </Button>
  )
}
