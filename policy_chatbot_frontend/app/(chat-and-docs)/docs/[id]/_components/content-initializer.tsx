'use client'

import { useId, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

import { PendingButton } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

import { DocContentEditor } from '../../_components/doc-content-editor'
import { DocumentRepository } from '../../api'

type FormFields = { content: string }

export function ContentInitializer({ documentId }: { documentId: string }) {
  const formId = useId()
  const [submitting, setSubmitting] = useState(false)

  async function handleSubmit({ content }: FormFields) {
    setSubmitting(true)
    try {
      await DocumentRepository.importChunks(documentId, content)
      toast.success('文档内容导入成功')
    } catch (e) {
      toast.error('文档内容导入失败', { description: String(e) })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="mx-auto mt-8 max-w-3xl">
      <Card>
        <CardHeader>
          <CardTitle>导入文档内容</CardTitle>
          <CardDescription>当前文档暂无内容，请导入内容。</CardDescription>
        </CardHeader>
        <CardContent>
          <ContentUploadForm documentId={documentId} formId={formId} onSubmit={handleSubmit} />
        </CardContent>
        <CardFooter className="justify-end">
          <PendingButton type="submit" form={formId} variant="default" pending={submitting}>
            保存
          </PendingButton>
        </CardFooter>
      </Card>
    </div>
  )
}

function ContentUploadForm({
  formId,
  onSubmit,
}: {
  onSubmit: (data: FormFields) => Promise<void>
  formId?: string
  documentId: string
}) {
  const form = useForm<FormFields>({
    defaultValues: {
      content: '',
    },
  })

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4" id={formId}>
          <FormField
            control={form.control}
            name="content"
            rules={{
              required: '文档内容不能为空',
            }}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="sr-only">文档内容</FormLabel>
                <FormControl>
                  <DocContentEditor {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </>
  )
}
