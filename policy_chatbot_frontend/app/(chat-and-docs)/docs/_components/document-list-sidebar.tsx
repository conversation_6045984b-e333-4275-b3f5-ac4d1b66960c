'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { Trash2Icon } from 'lucide-react'

import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'

import { useDocuments } from '../_providers/documents-provider'

export function DocumentListSidebar() {
  const { recentDocuments, clearRecent } = useDocuments()
  const { id } = useParams<{ id?: string }>()

  return (
    <>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>最近打开</SidebarGroupLabel>
          <SidebarGroupAction title="清除最近打开" onClick={clearRecent}>
            <Trash2Icon />
            <span className="sr-only">清除最近打开</span>
          </SidebarGroupAction>
          <SidebarGroupContent>
            <SidebarMenu>
              {recentDocuments.length === 0 && (
                <SidebarMenuItem className="text-muted-foreground p-4 text-center text-xs">
                  暂无最近打开的文档
                </SidebarMenuItem>
              )}
              {recentDocuments.map((doc) => (
                <SidebarMenuItem key={doc.id}>
                  <SidebarMenuButton isActive={id === String(doc.id)} asChild>
                    <Link href={`/docs/${doc.id}?from=recent`}>
                      <span>{doc.source}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </>
  )
}
