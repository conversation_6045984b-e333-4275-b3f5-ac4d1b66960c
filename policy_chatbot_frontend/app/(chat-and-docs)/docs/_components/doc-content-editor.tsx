'use client'

import { useState } from 'react'

import { Textarea } from '@/components/ui/textarea'

import { DocParser } from './doc-parser'

export function DocContentEditor({
  ref,
  onBlur,
  value,
  onChange,
  onInfoChange,
}: {
  ref?: React.Ref<HTMLTextAreaElement>
  value: string
  onBlur?: () => void
  onChange: (content: string) => void
  onInfoChange?: (info: { name: string; lastModified: Date }) => void
}) {
  const [contentIsFromFile, setContentIsFromFile] = useState(false)
  return (
    <div className="space-y-2">
      <DocParser
        onInfoChange={onInfoChange}
        onContentParsed={(content) => {
          const oldContent = value
          if (content === oldContent) return
          if (oldContent && !confirm('文档内容已存在，是否覆盖？')) return
          onChange(content)
          setContentIsFromFile(true)
        }}
      />
      <p className="text-muted-foreground text-sm">
        {contentIsFromFile ? '请检查解析结果：' : '你也可以手动填写内容：'}
      </p>
      <Textarea
        ref={ref}
        value={value}
        onBlur={onBlur}
        onChange={(e) => onChange(e.target.value)}
        className="h-[min(20rem,90vh)]"
      />
    </div>
  )
}
