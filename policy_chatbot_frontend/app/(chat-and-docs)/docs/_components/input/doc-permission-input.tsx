import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'

import type { PermissionRole } from '../../dto'

const ROLE_NAME_LEVEL_MAP: Record<string, number | null> = {
  admin: 1,
  everyone: null,
}
const ROLE_LEVEL_NAME_MAP = new Map(Object.entries(ROLE_NAME_LEVEL_MAP).map(([key, value]) => [value, key]))

export function DocPermissionInput({
  value,
  onChange,
  disabled,
}: {
  value?: PermissionRole
  onChange?: (value: number | null) => void
  disabled?: boolean
}) {
  return (
    <ToggleGroup
      type="single"
      variant="outline"
      color="primary"
      disabled={disabled}
      value={value !== undefined ? ROLE_LEVEL_NAME_MAP.get(value) : undefined}
      onValueChange={onChange && ((value) => onChange(ROLE_NAME_LEVEL_MAP[value]))}
    >
      <ToggleGroupItem value="admin" className="min-w-16">
        系统管理员
      </ToggleGroupItem>
      <ToggleGroupItem value="everyone" className="min-w-16">
        所有人
      </ToggleGroupItem>
    </ToggleGroup>
  )
}
