import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'

export function DocStatusInput({
  value,
  onChange,
  disabled,
}: {
  value?: boolean
  onChange?: (value: boolean) => void
  disabled?: boolean
}) {
  return (
    <ToggleGroup
      type="single"
      variant="outline"
      color="primary"
      disabled={disabled}
      value={value ? 'enabled' : 'disabled'}
      onValueChange={onChange && ((value) => onChange(value === 'enabled'))}
    >
      <ToggleGroupItem value="disabled" className="min-w-16">
        不启用
      </ToggleGroupItem>
      <ToggleGroupItem value="enabled" className="min-w-16">
        启用
      </ToggleGroupItem>
    </ToggleGroup>
  )
}
