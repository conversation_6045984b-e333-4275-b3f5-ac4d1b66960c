import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'

export type DocumentType = 'normal' | 'qa'

export function DocTypeInput({
  value,
  onChange,
  disabled,
}: {
  value?: DocumentType
  onChange?: (value: DocumentType) => void
  disabled?: boolean
}) {
  return (
    <ToggleGroup
      type="single"
      variant="outline"
      color="primary"
      disabled={disabled}
      value={value}
      onValueChange={onChange}
    >
      <ToggleGroupItem value="normal" className="min-w-16">
        普通文档
      </ToggleGroupItem>
      <ToggleGroupItem value="qa" className="min-w-16">
        问答文档
      </ToggleGroupItem>
    </ToggleGroup>
  )
}
