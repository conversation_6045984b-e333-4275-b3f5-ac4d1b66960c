import Link from 'next/link'
import { useQueryClient } from '@tanstack/react-query'
import { EllipsisIcon, PencilIcon, Trash2Icon } from 'lucide-react'
import { toast } from 'sonner'

import { truncateWords } from '@/lib/string'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Button, PromiseButton } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Skeleton, SkeletonText } from '@/components/ui/skeleton'

import { DocumentRepository } from '../api'
import { Document } from '../dto'

export function DocumentItem({ document, at }: { document: Document; at: string }) {
  return (
    <li key={document.id} className="relative not-last:border-b">
      <Link className="hover:bg-accent flex flex-col py-3 pr-8 pl-4" href={`/docs/${document.id}?from=${at}`}>
        <div className="text-sm font-semibold">{document.source}</div>
        <div className="text-sm">{document.version}</div>
        <div className="text-muted-foreground mt-1 text-xs">{document.createdAt.toLocaleDateString()}</div>
      </Link>
      <DocumentActions document={document} className="absolute top-1/2 right-4 -translate-y-1/2" />
    </li>
  )
}

export function DocumentListSkeleton() {
  return (
    <li className="not-last:border-b">
      <div className="flex flex-col px-4 py-3">
        <SkeletonText size="sm">
          <Skeleton className="w-1/2" />
        </SkeletonText>
        <SkeletonText size="sm">
          <Skeleton className="w-1/4" />
        </SkeletonText>
        <SkeletonText size="xs" className="mt-1">
          <Skeleton className="w-1/6" />
        </SkeletonText>
      </div>
    </li>
  )
}

function DocumentActions({ document, className }: { document: Document; className?: string }) {
  const queryClient = useQueryClient()
  const truncatedTitle = truncateWords(document.source, 10)
  const truncated = truncatedTitle !== document.source

  return (
    <AlertDialog>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size="icon" variant="ghost" onClick={() => {}} className={className}>
            <EllipsisIcon />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>
            <PencilIcon />
            编辑信息
          </DropdownMenuItem>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem variant="destructive">
              <Trash2Icon />
              删除文档
            </DropdownMenuItem>
          </AlertDialogTrigger>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            确认要删除
            {truncatedTitle ? (
              <>“{truncated ? <abbr title={document.source}>{truncatedTitle}</abbr> : truncatedTitle}”</>
            ) : (
              '该文档'
            )}
            吗？
          </AlertDialogTitle>
          <AlertDialogDescription>删除文档后将无法恢复。</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction variant="destructive" asChild>
            <PromiseButton
              onClick={async () => {
                try {
                  await DocumentRepository.delete(document.id)
                  await queryClient.invalidateQueries({ queryKey: ['documents', 'list'] })
                  toast.success('删除成功')
                } catch (e) {
                  toast.error(`删除失败：${e}`)
                }
              }}
            >
              删除
            </PromiseButton>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
