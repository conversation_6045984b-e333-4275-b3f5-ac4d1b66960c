'use client'

import { useCallback, useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { CheckIcon, RotateCcwIcon, UploadIcon, XIcon } from 'lucide-react'
import { toast } from 'sonner'
import { match } from 'ts-pattern'

import { truncateWords } from '@/lib/string'
import { Button } from '@/components/ui/button'
import {
  FileUpload,
  FileUploadDropzone,
  FileUploadItem,
  FileUploadItemMetadata,
  FileUploadItemPreview,
  FileUploadList,
  FileUploadTrigger,
} from '@/components/ui/file-upload'
import { Spinner } from '@/components/ui/spinner'

import { parseDocument } from '../api'

function splitFileNameAndExt(fileName: string): [name: string, ext: string | undefined] {
  const match = fileName.match(/^(.*)\.(.+?)$/)
  const name = match?.[1] ?? fileName
  const ext = match?.[2]
  return [name, ext]
}

interface DocInfo {
  name: string
  lastModified: Date
}

function getDocInfo(file: File): DocInfo {
  return {
    name: splitFileNameAndExt(file.name)[0],
    lastModified: new Date(file.lastModified),
  }
}

export function DocParser({
  onInfoChange,
  onContentParsed,
}: {
  onInfoChange?: (docInfo: DocInfo) => void
  onContentParsed: (content: string) => void
}) {
  const [file, setFile] = useState<File | null>(null)
  const { data, mutate, isError, error, status } = useMutation({
    mutationFn: async (file: File) => {
      const content = await parseDocument(file)
      onContentParsed(content)
      return content
    },
  })

  const onFileReject = useCallback((file: File, message: string) => {
    const [name, ext] = splitFileNameAndExt(file.name)
    toast(message, {
      description: `拒绝上传“${truncateWords(name, 10)}${ext ? `.${ext}` : ''}”`,
    })
  }, [])

  return (
    <div>
      <FileUpload
        value={file ? [file] : []}
        onValueChange={(files) => {
          const file = files.at(-1)
          if (!file) {
            setFile(null)
            return
          }
          setFile(file)
          onInfoChange?.(getDocInfo(file))
          mutate(file)
        }}
        onFileReject={onFileReject}
        accept=".doc,.docx,.pdf,.md,.markdown,.txt"
      >
        {!file && (
          <FileUploadDropzone>
            <div className="flex flex-col items-center gap-1">
              <div className="flex items-center justify-center rounded-full border p-2.5">
                <UploadIcon className="text-muted-foreground size-6" />
              </div>
              <p className="font-medium">将文件拖放至此处以读取内容</p>
              <p className="text-muted-foreground text-sm">或点击以浏览文件</p>
            </div>
            <FileUploadTrigger asChild>
              <Button variant="outline" size="sm" className="mt-2 w-fit">
                浏览文件
              </Button>
            </FileUploadTrigger>
          </FileUploadDropzone>
        )}

        {file && (
          <div className="grid grid-cols-[1fr_auto] gap-2">
            <FileUploadList>
              <FileUploadItem value={file}>
                <FileUploadItemPreview />
                <FileUploadItemMetadata />
                <div className="text-muted-foreground grid justify-items-center gap-1 text-xs">
                  {match(status)
                    .with('success', () => (
                      <>
                        <CheckIcon className="text-foreground size-4" />
                        解析成功
                      </>
                    ))
                    .with('pending', () => (
                      <>
                        <Spinner className="text-foreground size-4" />
                        解析中
                      </>
                    ))
                    .with('error', () => (
                      <>
                        <XIcon className="text-destructive size-4" />
                        解析失败
                      </>
                    ))
                    .run()}
                </div>
              </FileUploadItem>
            </FileUploadList>
            <FileUploadTrigger asChild>
              <Button variant="outline" size="xs" className="h-auto flex-col items-center justify-center">
                <RotateCcwIcon className="size-4" />
                重新选择
              </Button>
            </FileUploadTrigger>
          </div>
        )}
      </FileUpload>

      {isError && <p className="text-destructive text-sm">解析失败：{error.message || '未知错误'}</p>}
      {data === '' && <p className="text-destructive text-sm">解析内容为空</p>}
    </div>
  )
}
