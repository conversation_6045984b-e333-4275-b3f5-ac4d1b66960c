'use client'

import { useState } from 'react'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { ArrowRight, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'

import { LoadError, LoadErrorDescription, LoadErrorRetryTrigger, LoadErrorTitle } from '@/components/app-ui/load-error'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { usePaginate } from '@/hooks/use-paginate'

import { DocumentRepository } from '../api'
import { DocumentItem, DocumentListSkeleton } from './doc-item'

export function DocList() {
  const { page, skip, limit, hasNext, hasPrev, next, prev, setTotal, totalPages, setPage } = usePaginate({
    defaultLimit: 15,
  })

  const { data, error, isError, isPending, refetch } = useQuery({
    queryKey: ['documents', 'list', skip, limit],
    queryFn: async () => {
      const { documents, total } = await DocumentRepository.list({ skip, limit })
      setTotal(total)
      return documents
    },
    placeholderData: keepPreviousData,
  })

  if (isPending) {
    return (
      <ul className="mx-auto flex max-w-4xl flex-col rounded-md border">
        {Array.from({ length: 10 }, (_, index) => (
          <DocumentListSkeleton key={index} />
        ))}
      </ul>
    )
  }

  if (isError) {
    return (
      <div className="mx-auto max-w-4xl">
        <LoadError>
          <LoadErrorTitle>获取文档列表时发生错误</LoadErrorTitle>
          <LoadErrorDescription>{error.message}</LoadErrorDescription>
          <LoadErrorRetryTrigger onClick={() => refetch()} />
        </LoadError>
      </div>
    )
  }

  return (
    <>
      <ul className="mx-auto flex max-w-4xl flex-col rounded-md border">
        {data.map((document) => (
          <DocumentItem key={document.id} document={document} at={`page-${page}`} />
        ))}
      </ul>
      <div className="mt-4 flex items-center justify-center">
        <Button variant="ghost" size="icon" disabled={!hasPrev} onClick={prev}>
          <ChevronLeftIcon />
          <span className="sr-only">上一页</span>
        </Button>
        <PageChangeInput page={page} totalPages={totalPages} onPageChange={setPage} />
        <Button variant="ghost" size="icon" disabled={!hasNext} onClick={next}>
          <ChevronRightIcon />
          <span className="sr-only">下一页</span>
        </Button>
      </div>
    </>
  )
}

function PageChangeInput({
  page,
  totalPages,
  onPageChange,
}: {
  page: number
  totalPages: number
  onPageChange: (page: number) => void
}) {
  const [open, setOpen] = useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="flex items-center gap-2 text-sm">
          <span>{page}</span>
          <span>/</span>
          <span>{totalPages}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="py-0 pr-0 pl-3">
        <form
          className="-my-px flex items-center gap-1.5"
          onSubmit={(e) => {
            e.preventDefault()
            const input = (e.target as HTMLElement).querySelector('input')
            if (input) onPageChange(Number(input.value))
            setOpen(false)
          }}
        >
          <div className="text-sm">跳转至第</div>
          <input
            defaultValue={page}
            className="placeholder:text-muted-foreground border-input focus-visible:border-ring focus-visible:ring-ring/50 h-9 border px-2 text-sm font-medium transition-[color,box-shadow] outline-none focus-visible:ring-[3px]"
            type="number"
            max={totalPages}
            min={1}
            placeholder={`${page}`}
          />
          <div className="mr-2 text-sm">页</div>
          <Button type="submit" className="-mr-px rounded-l-none border" variant="ghost" size="icon">
            <ArrowRight />
          </Button>
        </form>
      </PopoverContent>
    </Popover>
  )
}
