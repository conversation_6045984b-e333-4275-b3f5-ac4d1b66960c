'use client'

import { ComponentProps } from 'react'
import { PlusIcon } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Tooltip } from '@/components/ui/tooltip'
import { useThreadList } from '@/providers/thread-list-provider'

export function NewThreadTrigger({ className, ...props }: ComponentProps<typeof Button>) {
  const { switchToNewThread } = useThreadList()

  return (
    <Tooltip tip="新对话" asChild>
      <Button className={cn('size-7', className)} size="icon" variant="ghost" onClick={switchToNewThread} {...props}>
        <PlusIcon />
      </Button>
    </Tooltip>
  )
}
