'use client'

import { useState } from 'react'
import { MoreHorizontalIcon, PlusIcon, TextCursorInputIcon, Trash2Icon } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { Spinner } from '@/components/ui/spinner'
import { ChatThread, useThreadList } from '@/providers/thread-list-provider'

export function ThreadListSidebar() {
  const { currentThreadID, newThreadID, switchToNewThread, switchToThread, threads, isPending } = useThreadList()

  return (
    <>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={currentThreadID === newThreadID}
              className="font-medium"
              onClick={switchToNewThread}
            >
              <PlusIcon />
              <span>新对话</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {isPending ? (
                <div className="flex items-center justify-center py-6">
                  <Spinner className="text-muted-foreground size-4" />
                </div>
              ) : (
                threads.map((thread) => (
                  <SidebarMenuItem key={thread.id}>
                    <SidebarMenuButton
                      className=""
                      onClick={() => switchToThread(thread.id)}
                      isActive={currentThreadID === thread.id}
                    >
                      <span>{thread.title || '新聊天'}</span>
                    </SidebarMenuButton>
                    <ThreadItemAction thread={thread} />
                  </SidebarMenuItem>
                ))
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </>
  )
}

function ThreadItemAction({ thread }: { thread: ChatThread }) {
  const { deleteThread, currentThreadID, switchToNewThread, renameThread } = useThreadList()

  const [renameOpen, setRenameOpen] = useState(false)

  return (
    <DropdownMenu>
      <Dialog open={renameOpen} onOpenChange={setRenameOpen}>
        <AlertDialog>
          <DropdownMenuTrigger className="data-[state=open]:opacity-100" asChild>
            <SidebarMenuAction className="opacity-0 group-hover/menu-item:opacity-100 focus-visible:opacity-100 max-md:opacity-100">
              <MoreHorizontalIcon />
            </SidebarMenuAction>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="right" align="start">
            <DialogTrigger asChild>
              <DropdownMenuItem>
                <TextCursorInputIcon />
                重命名对话
              </DropdownMenuItem>
            </DialogTrigger>
            <DropdownMenuSeparator />
            <AlertDialogTrigger asChild>
              <DropdownMenuItem variant="destructive">
                <Trash2Icon />
                删除对话
              </DropdownMenuItem>
            </AlertDialogTrigger>
          </DropdownMenuContent>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>重命名对话</DialogTitle>
              <DialogDescription className="sr-only">重命名对话：{thread.title}</DialogDescription>
            </DialogHeader>
            <form
              className="grid gap-4"
              onSubmit={(e) => {
                e.preventDefault()
                const newName = (e.currentTarget[0] as HTMLInputElement).value
                renameThread({ id: thread.id, name: newName }).then(() => setRenameOpen(false))
              }}
            >
              <Input defaultValue={thread.title} />
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    取消
                  </Button>
                </DialogClose>
                <Button type="submit">保存</Button>
              </DialogFooter>
            </form>
          </DialogContent>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认要删除对话吗？</AlertDialogTitle>
              <AlertDialogDescription>删除对话后将无法恢复。</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction
                variant="destructive"
                onClick={async () => {
                  await deleteThread(thread.id)
                  if (currentThreadID === thread.id) {
                    switchToNewThread()
                  }
                }}
              >
                继续
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Dialog>
    </DropdownMenu>
  )
}
