'use client'

import * as React from 'react'
import { AlignLeftIcon } from 'lucide-react'

import { App<PERSON><PERSON><PERSON>, AppHeaderTitle } from '@/components/app-ui/app-header'
import { Thread } from '@/components/assistant-ui/thread'
import { DecisionUI } from '@/components/tool-ui/decision-ui'
import { ReasoningContentUI } from '@/components/tool-ui/reasoning-content'
import { ReferencesUI } from '@/components/tool-ui/references-ui'
import { RetrieveDocumentUI } from '@/components/tool-ui/retrieve-document'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { AssistantRuntimeProvider } from '@/providers/runtime-provider'
import { useThreadList } from '@/providers/thread-list-provider'

import { NewThreadTrigger } from './_components/new-thread-trigger'
import { ThreadListSidebar } from './_components/thread-list-sidebar'

export default function Home() {
  const { currentThreadID } = useThreadList()

  return (
    <AssistantRuntimeProvider key={currentThreadID}>
      <Thread />
      <AppHeader className="md:hidden">
        <ThreadListSheet />
        <AppHeaderTitle>对话</AppHeaderTitle>
        <NewThreadTrigger className="ml-auto" />
      </AppHeader>
      <RetrieveDocumentUI />
      <ReferencesUI />
      <ReasoningContentUI />
      <DecisionUI />
    </AssistantRuntimeProvider>
  )
}

function ThreadListSheet() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" className="-ml-1 size-7 p-0">
          <AlignLeftIcon />
        </Button>
      </SheetTrigger>
      <SheetHeader className="sr-only">
        <SheetTitle>Thread list</SheetTitle>
        <SheetDescription>Displays the mobile thread list.</SheetDescription>
      </SheetHeader>
      <SheetContent side="left" className="bg-sidebar text-sidebar-foreground w-[15rem] p-0 [&>button]:hidden">
        <div className="flex h-full w-full flex-col">
          <ThreadListSidebar />
        </div>
      </SheetContent>
    </Sheet>
  )
}
