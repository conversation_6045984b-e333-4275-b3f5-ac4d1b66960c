'use client'

import { ComponentProps } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { BookTextIcon, MessageSquareIcon } from 'lucide-react'

import { SidebarTriggerTooltip } from '@/components/app-ui/sidebar-trigger-tooltip'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'

import { ThreadListSidebar } from '../(chat)/_components/thread-list-sidebar'
import { DocumentListSidebar } from '../docs/_components/document-list-sidebar'

const navMain = [
  {
    title: '对话',
    href: '/',
    icon: <MessageSquareIcon />,
    sidebar: <ThreadListSidebar />,
  },
  {
    title: '文档库',
    href: '/docs',
    icon: <BookTextIcon />,
    sidebar: <DocumentListSidebar />,
  },
]

export function AppSidebar({ ...props }: ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const currentItem = '/' + (pathname.split('/')[1] || '')

  const { setOpenMobile } = useSidebar()

  return (
    <Sidebar collapsible="icon" className="overflow-hidden [&>[data-sidebar=sidebar]]:flex-row" {...props}>
      <Sidebar collapsible="none" className="!w-[calc(var(--sidebar-width-icon)+1px)] border-r">
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent className="px-1.5 md:px-0">
              <SidebarMenu>
                {navMain.map((item) => (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      tooltip={{
                        children: item.title,
                        hidden: false,
                      }}
                      isActive={currentItem === item.href}
                      onClick={() => setOpenMobile(false)}
                      className="px-2.5 md:px-2"
                      asChild
                    >
                      <Link href={item.href}>
                        {item.icon}
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarTriggerTooltip className="size-8 max-md:hidden" tooltip={{ side: 'right' }} />
        </SidebarFooter>
      </Sidebar>

      <Sidebar collapsible="none" className="hidden min-w-0 flex-1 md:flex">
        {navMain.find((item) => item.href === currentItem)?.sidebar}
      </Sidebar>
    </Sidebar>
  )
}
