import React from 'react'

import { AuthDetector } from '@/components/auth-detector'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { ThreadListProvider } from '@/providers/thread-list-provider'

import { AppSidebar } from './_components/app-sidebar'
import { DocumentsProvider } from './docs/_providers/documents-provider'

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      <ThreadListProvider>
        <DocumentsProvider>
          <SidebarProvider style={{ '--sidebar-width': '280px' } as React.CSSProperties}>
            <AppSidebar />
            <SidebarInset>{children}</SidebarInset>
          </SidebarProvider>
        </DocumentsProvider>
      </ThreadListProvider>
      <AuthDetector />
    </>
  )
}
