import type { Metadata } from 'next'
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google'

import './globals.css'
import 'katex/dist/katex.min.css'

import React from 'react'
import { Provider as <PERSON><PERSON><PERSON>rovider } from 'jotai'
import { NuqsAdapter } from 'nuqs/adapters/next/app'

import { cn } from '@/lib/utils'
import { Toaster } from '@/components/ui/sonner'
import { TooltipProvider } from '@/components/ui/tooltip'
import { QueryProvider } from '@/providers/query-provider'
import { ThemeProvider } from '@/providers/theme-provider'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: '知识库问答助手',
  description: '知识库问答助手',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <body className={cn(geistSans.variable, geistMono.variable, 'font-sans antialiased')} suppressHydrationWarning>
        <NuqsAdapter>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            <JotaiProvider>
              <QueryProvider>
                <TooltipProvider>
                  {children}
                  <Toaster />
                </TooltipProvider>
              </QueryProvider>
            </JotaiProvider>
          </ThemeProvider>
        </NuqsAdapter>
      </body>
    </html>
  )
}
