import { client } from '@/lib/request'

export interface LoginParams {
  username: string
  password: string
}

export async function login(params: LoginParams): Promise<void> {
  const { access_token: accessToken } = await client
    .post<{ access_token: string; token_type: 'Bearer' }>('auth/login', { body: new URLSearchParams(params as any) })
    .json()
  localStorage.setItem('accessToken', accessToken)
}
