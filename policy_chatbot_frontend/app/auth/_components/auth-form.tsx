'use client'

import { useRouter } from 'next/navigation'
import { LandmarkIcon } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

import { But<PERSON>, PendingButton } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { SeparatorWithText } from '@/components/ui/separator'

import { login, LoginParams } from '../api'

export function LoginForm() {
  const form = useForm<LoginParams>()
  const router = useRouter()

  return (
    <form
      onSubmit={form.handleSubmit(async (params) => {
        try {
          await login(params)
        } catch (e) {
          toast.error('登录失败', { description: String(e) })
          return
        }
        router.push('/')
      })}
    >
      <div className="grid gap-6">
        <div className="grid gap-6">
          <div className="grid gap-2">
            <Label htmlFor="username">用户名</Label>
            <Input id="username" type="text" {...form.register('username', { required: true })} />
          </div>
          <div className="grid gap-2">
            <div className="flex items-center">
              <Label htmlFor="password">密码</Label>
              <a href="#" className="ml-auto text-sm underline-offset-4 hover:underline" hidden>
                忘记密码？
              </a>
            </div>
            <Input id="password" type="password" {...form.register('password', { required: true })} />
          </div>
          <PendingButton type="submit" className="w-full" pending={form.formState.isSubmitting}>
            登录
          </PendingButton>
        </div>
        <div className="text-muted-foreground text-center text-sm">
          {/* 没有账号？
						<a href="#" className="underline underline-offset-4">
							注册
						</a> */}
          暂未开放注册，敬请期待
        </div>
      </div>
    </form>
  )
}

export function RegisterForm() {
  return null
}
