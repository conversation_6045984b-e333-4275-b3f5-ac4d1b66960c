import { useCallback, useState } from 'react'
import { parseAsInteger, useQueryState } from 'nuqs'

interface UsePaginateOptions {
  defaultTotal?: number
  defaultPage?: number
  defaultLimit?: number
}

interface UsePaginateResult {
  page: number
  setPage: (page: number) => void
  limit: number
  setLimit: (pageSize: number) => void
  skip: number
  head: () => void
  tail: () => void
  next: () => void
  prev: () => void
  hasNext: boolean
  hasPrev: boolean
  total: number
  setTotal: (total: number) => void
  totalPages: number
}

export function usePaginate(opts: UsePaginateOptions = {}): UsePaginateResult {
  const { defaultPage = 1, defaultLimit = 10, defaultTotal = Infinity } = opts

  const [total, _setTotal] = useState(defaultTotal)

  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(defaultPage))
  const [limit, setLimit] = useQueryState('limit', parseAsInteger.withDefault(defaultLimit))

  const totalPages = Math.ceil(total / limit)
  const skip = (page - 1) * limit

  const setTotal: typeof _setTotal = useCallback(
    (value) => {
      const newTotal = value instanceof Function ? value(total) : value
      _setTotal(newTotal)
      const newTotalPages = Math.ceil(newTotal / limit)
      if (page !== 1 && page > newTotalPages) {
        setPage(newTotalPages)
      }
    },
    [_setTotal, setPage, total, page, limit],
  )

  const head = useCallback(() => {
    return setPage(1)
  }, [setPage])

  const tail = useCallback(() => {
    return setPage(totalPages)
  }, [setPage, totalPages])

  const hasNext = page < totalPages
  const hasPrev = page > 1

  const next = useCallback(() => {
    return setPage((prev) => (hasNext ? prev + 1 : prev))
  }, [hasNext, setPage])

  const prev = useCallback(() => {
    return setPage((prev) => (hasPrev ? prev - 1 : prev))
  }, [hasPrev, setPage])

  return {
    page,
    setPage,
    limit,
    setLimit,
    skip,
    head,
    tail,
    next,
    prev,
    hasNext,
    hasPrev,
    total,
    setTotal,
    totalPages,
  }
}
