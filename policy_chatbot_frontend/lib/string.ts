type TruncateWords = (str: string, maxSegments: number, options?: { ellipsis?: string }) => string

export const { truncateWords }: { truncateWords: TruncateWords } = (() => {
  // 适配不支持 Intl.Segmenter 的环境
  if (!Intl.Segmenter) {
    return {
      truncateWords: (str: string, maxSegments: number, { ellipsis = '…' }: { ellipsis?: string } = {}) => {
        const chars = [...str] // 按码位分割字符串
        if (chars.length <= maxSegments + 1) return str
        return chars.slice(0, maxSegments).join('') + ellipsis
      },
    }
  }

  const graphemeSegmenter = new Intl.Segmenter('zh-Hans-CN', { granularity: 'grapheme' })
  const wordSegmenter = new Intl.Segmenter('zh-Hans-CN', { granularity: 'word' })

  function splitGraphemeClusters(str: string) {
    return [...graphemeSegmenter.segment(str)].map((x) => x.segment)
  }

  /**
   * 按照单词数截断字符串（空格、标点也算）
   * @param str The string to truncate.
   * @param maxSegments The maximum segments of the string.
   */
  const truncateWords: TruncateWords = (
    str: string,
    maxSegments: number,
    { ellipsis = '…' }: { ellipsis?: string } = {},
  ) => {
    const segments = [...wordSegmenter.segment(str)]
    if (segments.length <= maxSegments) return str

    // 如果仅超出一个词，且最后一个词是单个字形簇，则不截断，因为就算替换为省略号也不会使长度变短
    if (segments.length === maxSegments + 1 && splitGraphemeClusters(segments[maxSegments].segment).length === 1)
      return str

    return (
      segments
        .slice(0, maxSegments)
        .map((segment) => segment.segment)
        .join('')
        .trimEnd() + ellipsis
    )
  }

  return {
    truncateWords,
  }
})()
