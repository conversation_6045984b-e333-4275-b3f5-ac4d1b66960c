import ky from 'ky'

import { emitter } from './emitter'

export const client = ky.create({
  prefixUrl: process.env.NEXT_PUBLIC_BASE_URL,
  hooks: {
    beforeRequest: [
      (request) => {
        const accessToken = localStorage.getItem('accessToken')
        if (!accessToken) return
        request.headers.set('Authorization', `Bearer ${accessToken}`)
      },
    ],
    afterResponse: [
      (_request, _options, response) => {
        if (response.status === 401) {
          localStorage.removeItem('accessToken')
          emitter.emit('token-failed')
        }
      },
    ],
  },
})
