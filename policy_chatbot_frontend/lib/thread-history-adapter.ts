import { ThreadMessage } from '@assistant-ui/react'

type ThreadHistoryAdapter = {
  load(): Promise<MessageRepository>
  append(item: MessageRepositoryItem): Promise<void>
}

interface MessageRepository {
  headId: string | null
  messages: MessageRepositoryItem[]
}

interface MessageRepositoryItem {
  message: ThreadMessage
  parentId: string | null
}

type ThreadHistoryAdapterOptions = {
  threadId: string
  onNewMessage?: (message: MessageRepositoryItem) => void
}

export const createThreadHistoryAdapter = (opts: ThreadHistoryAdapterOptions): ThreadHistoryAdapter => {
  const { threadId, onNewMessage } = opts
  return {
    async load(): Promise<MessageRepository> {
      const histories = JSON.parse(localStorage.getItem(`history-${threadId}`) || '[]') as MessageRepositoryItem[]
      return {
        headId: null,
        messages: histories,
      }
    },
    async append(item: MessageRepositoryItem): Promise<void> {
      onNewMessage?.(item)
      const histories = JSON.parse(localStorage.getItem(`history-${threadId}`) || '[]') as MessageRepositoryItem[]
      localStorage.setItem(`history-${threadId}`, JSON.stringify([...histories, item]))
    },
  }
}
