import { FeedbackAdapter } from '@assistant-ui/react'
import { toast } from 'sonner'
import { match } from 'ts-pattern'

import { client } from '@/lib/request'

export const createFeedbackAdapter = (): FeedbackAdapter => {
  return {
    submit: async ({ message, type }) => {
      const contentType = match(type)
        .with('positive', () => '点赞')
        .with('negative', () => '点踩')
        .exhaustive()
      await sendMessageFeedback({ message_id: message.id, content: contentType }).catch((err: Error) =>
        toast.error(err.message),
      )
    },
  }
}

export async function sendMessageFeedback(value: { message_id: string; content: string }) {
  await client.post('chat/feedback', { json: value })
}
