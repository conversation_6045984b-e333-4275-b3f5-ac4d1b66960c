{"name": "policy-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai-compatible": "^0.1.17", "@assistant-ui/react": "^0.7.91", "@assistant-ui/react-ai-sdk": "^0.7.16", "@assistant-ui/react-markdown": "^0.7.21", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.74.0", "@tanstack/react-table": "^8.21.2", "ai": "^4.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.12.3", "katex": "^0.16.22", "ky": "^1.8.1", "lucide-react": "^0.487.0", "mitt": "^3.0.1", "nanoid": "^5.1.5", "next": "15.3.0", "next-themes": "^0.4.6", "nuqs": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "ts-pattern": "^5.7.0", "usehooks-ts": "^3.1.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@next/eslint-plugin-next": "^15.3.2", "@tailwindcss/postcss": "^4.1.3", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.73.3", "@types/node": "^20.17.30", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "15.3.0", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.3", "typescript": "^5.8.3"}}