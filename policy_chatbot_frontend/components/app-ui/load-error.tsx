'use client'

import { ComponentProps } from 'react'
import { RotateCcwIcon } from 'lucide-react'

import { cn } from '@/lib/utils'
import { PromiseButton } from '@/components/ui/button'

export function LoadError({ className, children, ...props }: ComponentProps<'div'>) {
  return (
    <div className={cn('bg-destructive/5 rounded-lg px-4 py-3', className)} {...props}>
      {children}
    </div>
  )
}

export function LoadErrorTitle({ className, children, ...props }: ComponentProps<'h2'>) {
  return (
    <h3 className={cn('text-destructive text-base font-semibold', className)} {...props}>
      {children}
    </h3>
  )
}

export function LoadErrorDescription({ className, children, ...props }: ComponentProps<'p'>) {
  return (
    <p className={cn('text-foreground/60 text-sm', className)} {...props}>
      {children}
    </p>
  )
}

export function LoadErrorRetryTrigger({ className, ...props }: ComponentProps<typeof PromiseButton>) {
  return (
    <PromiseButton
      className={cn('border-input hover:bg-foreground/5 mt-3 mb-1 border', className)}
      icon={<RotateCcwIcon />}
      size="sm"
      variant="ghost"
      {...props}
    >
      重试
    </PromiseButton>
  )
}
