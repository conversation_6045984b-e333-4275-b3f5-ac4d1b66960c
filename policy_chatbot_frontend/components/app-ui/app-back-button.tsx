'use client'

import { ComponentProps } from 'react'
import { useRouter } from 'next/navigation'
import { ChevronLeftIcon } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tooltip } from '@/components/ui/tooltip'

export function AppBackButton({ className, ...props }: ComponentProps<typeof Button>) {
  const { back } = useRouter()

  return (
    <>
      <Tooltip tip="返回" asChild>
        <Button className={cn('-ml-1 size-7', className)} variant="ghost" size="icon" onClick={back} {...props}>
          <ChevronLeftIcon />
        </Button>
      </Tooltip>
      <Separator orientation="vertical" className="mr-2 !h-4 max-md:hidden" />
    </>
  )
}
