import { ComponentProps } from 'react'

import { cn } from '@/lib/utils'
import { SidebarTriggerTooltip } from '@/components/app-ui/sidebar-trigger-tooltip'
import { Separator } from '@/components/ui/separator'

export function AppHeader({ className, children }: ComponentProps<'header'>) {
  return (
    <header
      className={cn('bg-background sticky top-0 flex h-12 shrink-0 items-center gap-2 border-b px-3.5', className)}
    >
      <SidebarTriggerTooltip className="-ml-1 md:hidden" />
      <Separator orientation="vertical" className="mr-2 !h-4 md:hidden" />
      {children}
    </header>
  )
}

export function AppHeaderTitle({ children, className, ...props }: ComponentProps<'h2'>) {
  return (
    <h2 className={cn('text-sm font-semibold', className)} {...props}>
      {children}
    </h2>
  )
}
