'use client'

import { ComponentProps } from 'react'

import { SidebarTrigger, useSidebar } from '@/components/ui/sidebar'
import { TooltipContent, TooltipRoot, TooltipTrigger } from '@/components/ui/tooltip'

export function SidebarTriggerTooltip({
  tooltip,
  ...props
}: ComponentProps<typeof SidebarTrigger> & {
  tooltip?: ComponentProps<typeof TooltipContent>
}) {
  const { open } = useSidebar()
  return (
    <TooltipRoot>
      <TooltipTrigger asChild>
        <SidebarTrigger {...props} />
      </TooltipTrigger>
      <TooltipContent {...tooltip}>{open ? '收起侧边栏' : '展开侧边栏'}</TooltipContent>
    </TooltipRoot>
  )
}
