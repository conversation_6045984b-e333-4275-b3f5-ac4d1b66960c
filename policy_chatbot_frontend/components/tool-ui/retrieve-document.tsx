import { makeAssistantToolUI } from '@assistant-ui/react'
import { match } from 'ts-pattern'

import { ToolCallingStep } from '@/components/tool-ui/tool-calling'

type RetrieveDocumentArgs = string

type RetrieveDocumentResult = string

export const RetrieveDocumentUI = makeAssistantToolUI<RetrieveDocumentArgs, RetrieveDocumentResult>({
  toolName: 'retrieve_document',
  render: ({ status, args }) => {
    return (
      <ToolCallingStep status={status}>
        {match(status)
          .with({ type: 'running' }, () => <div className="text-shimmer">正在检索“{`${args}`}”</div>)
          .with({ type: 'complete' }, () => <div>检索完成</div>)
          .with({ type: 'incomplete' }, () => <div>检索失败</div>)
          .otherwise(() => null)}
      </ToolCallingStep>
    )
  },
})
