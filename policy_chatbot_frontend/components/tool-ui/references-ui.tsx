import { makeAssistantToolUI } from '@assistant-ui/react'

type ReferencesArgs = object

type ReferencesResult = {
  id: number
  title: string
}[]

export const ReferencesUI = makeAssistantToolUI<ReferencesArgs, ReferencesResult>({
  toolName: 'generate_references',
  render: ({ result, status }) => {
    if (status.type !== 'complete' || !result) return null
    return (
      <div className="bg-muted/50 mt-4 rounded-lg px-4 py-3">
        <h3 className="text-muted-foreground text-sm font-semibold">检索到的文档</h3>
        <ol className="mt-2 grid grid-cols-[auto_minmax(0,1fr)] gap-x-2 gap-y-1 text-sm">
          {result.map((ref, index) => (
            <li key={ref.id} className="contents">
              <span className="text-muted-foreground pointer-events-none justify-self-end tabular-nums select-none">
                [{index + 1}]
              </span>
              <span>{ref.title}</span>
            </li>
          ))}
        </ol>
      </div>
    )
  },
})
