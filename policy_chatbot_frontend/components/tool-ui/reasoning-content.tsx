import { makeAssistantToolUI } from '@assistant-ui/react'
import { ChevronRightIcon } from 'lucide-react'
import { match } from 'ts-pattern'
import { useLocalStorage } from 'usehooks-ts'

import { Markdown } from '@/components/app-ui/markdown'
import { ToolCallingStatusIcon } from '@/components/tool-ui/tool-calling'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'

type ReasoningContentArgs = string

type ReasoningContentResult = string

export const ReasoningContentUI = makeAssistantToolUI<ReasoningContentArgs, ReasoningContentResult>({
  toolName: 'reasoning_content',
  render: ({ status, argsText, result }) => {
    /* eslint-disable react-hooks/rules-of-hooks */
    const statusText = match(status.type)
      .with('running', () => <div className="text-shimmer">正在思考</div>)
      .with('complete', () => <div>思考完成（用时 {result} 秒）</div>)
      .with('incomplete', () => <div>思考已停止</div>)
      .otherwise(() => null)
    const [open, setOpen] = useLocalStorage('reasoning-content', true)
    return (
      <Collapsible
        open={open}
        onOpenChange={setOpen}
        className="group/collapsible flex flex-col items-start gap-1 pb-1.5"
      >
        <CollapsibleTrigger className="text-muted-foreground hover:text-accent-foreground hover:bg-accent/50 -mx-1 -my-0.5 flex items-center gap-2 rounded-sm px-1 py-0.5 text-left text-sm transition-colors [&_svg]:size-4">
          <ToolCallingStatusIcon statusType={status.type} />
          {statusText}
          <ChevronRightIcon className="group-data-[state=open]/collapsible:rotate-90" />
        </CollapsibleTrigger>
        <CollapsibleContent className="pb-3">
          <Markdown content={argsText} className="prose-sm border-l-2 pl-3.5 opacity-80" />
        </CollapsibleContent>
      </Collapsible>
    )
  },
})
