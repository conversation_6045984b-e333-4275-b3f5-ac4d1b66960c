'use client'

import { ComponentProps } from 'react'
import { ContentPartState } from '@assistant-ui/react'
import { CheckI<PERSON>, CircleHelpIcon, XIcon } from 'lucide-react'
import { match } from 'ts-pattern'

import { Spinner } from '@/components/ui/spinner'

type Status = ContentPartState['status']

export const ToolCallingStep = ({
  children,
  status,
}: ComponentProps<'div'> & {
  status: Status
}) => {
  return (
    <div className="text-muted-foreground flex items-center gap-2 pb-1.5 text-sm [&_svg]:size-4">
      <ToolCallingStatusIcon statusType={status.type} />
      {children}
    </div>
  )
}

export const ToolCallingStatusIcon = ({ statusType }: { statusType: Status['type'] }) => {
  return (
    <>
      {match(statusType)
        .with('running', () => <Spinner />)
        .with('complete', () => <CheckIcon />)
        .with('incomplete', () => <XIcon />)
        .with('requires-action', () => <CircleHelpIcon />)
        .exhaustive()}
    </>
  )
}
