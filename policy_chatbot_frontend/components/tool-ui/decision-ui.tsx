import { makeAssistantToolUI } from '@assistant-ui/react'
import { match } from 'ts-pattern'

import { ToolCallingStep } from '@/components/tool-ui/tool-calling'

export const DecisionUI = makeAssistantToolUI({
  toolName: 'decide_action',
  render: ({ status }) => {
    if (status.type !== 'running') return null

    return (
      <ToolCallingStep status={status}>
        {match(status)
          .with({ type: 'running' }, () => <div className="text-shimmer">正在分析</div>)
          .otherwise(() => null)}
      </ToolCallingStep>
    )
  },
})
