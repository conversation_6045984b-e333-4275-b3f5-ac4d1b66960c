'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

import { emitter } from '@/lib/emitter'

export function AuthDetector() {
  const router = useRouter()

  useEffect(() => {
    if (!localStorage.getItem('accessToken')) {
      goToAuthPage()
      return
    }

    function goToAuthPage() {
      router.push('/auth')
    }

    emitter.on('token-failed', goToAuthPage)

    return () => {
      emitter.off('token-failed', goToAuthPage)
    }
  }, [router])

  return null
}
