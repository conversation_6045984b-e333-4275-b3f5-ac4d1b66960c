'use client'

import * as React from 'react'
import * as SeparatorPrimitive from '@radix-ui/react-separator'

import { cn } from '@/lib/utils'

function Separator({
  className,
  orientation = 'horizontal',
  decorative = true,
  ...props
}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {
  return (
    <SeparatorPrimitive.Root
      data-slot="separator-root"
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',
        className,
      )}
      {...props}
    />
  )
}

export function SeparatorWithText({
  className,
  lineClassName,
  children,
  orientation = 'horizontal',
  decorative = true,
  ...props
}: React.ComponentProps<typeof SeparatorPrimitive.Root> & { lineClassName?: string }) {
  const line = (
    <div
      className={cn(
        'bg-border flex-1 group-data-[orientation=horizontal]:h-px group-data-[orientation=vertical]:w-px',
        lineClassName,
      )}
    />
  )
  return (
    <SeparatorPrimitive.Root
      data-slot="separator-root"
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'group flex shrink-0 items-center gap-2 data-[orientation=vertical]:[writing-mode:vertical-rl]',
        className,
      )}
      {...props}
    >
      {line}
      {children}
      {line}
    </SeparatorPrimitive.Root>
  )
}

export { Separator }
