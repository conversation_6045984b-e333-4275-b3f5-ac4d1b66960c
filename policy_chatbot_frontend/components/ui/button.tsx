import { useCallback, useEffect, useState } from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

import { Spinner } from './spinner'

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40',
        outline: 'border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        xs: 'text-xs h-6 gap-1.5 px-2 has-[>svg]:px-2.5',
        sm: 'h-8 gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 px-6 has-[>svg]:px-4',
        icon: 'size-9',
        'icon-sm': 'size-8 rounded-md',
        'icon-xs': 'size-7 rounded-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : 'button'

  return <Comp data-slot="button" className={cn(buttonVariants({ variant, size, className }))} {...props} />
}

function PendingButton({
  pending,
  icon,
  children,
  disabled,
  ...props
}: {
  pending?: boolean
  icon?: React.ReactNode
} & React.ComponentProps<typeof Button>) {
  const [showSpinner, setShowSpinner] = useState(false)

  useEffect(() => {
    if (pending) {
      const timeoutId = setTimeout(setShowSpinner, 400, true)
      return () => {
        clearTimeout(timeoutId)
      }
    } else {
      setShowSpinner(false)
    }
  }, [pending])

  return (
    <Button {...props} disabled={pending !== false || disabled}>
      {showSpinner ? <Spinner /> : icon}
      {children}
    </Button>
  )
}

function PromiseButton({
  onClick,
  ...props
}: { onClick?: (event: React.MouseEvent<HTMLButtonElement>) => Promise<any>; icon?: React.ReactNode } & Omit<
  React.ComponentProps<typeof Button>,
  'onClick'
>) {
  const [pending, setPending] = useState(false)

  const handleClick = useCallback(
    async (e: React.MouseEvent<HTMLButtonElement>) => {
      setPending(true)
      try {
        await onClick?.(e)
      } finally {
        setPending(false)
      }
    },
    [onClick],
  )

  return <PendingButton {...props} pending={pending} onClick={handleClick} />
}

export { Button, PendingButton, PromiseButton, buttonVariants }
