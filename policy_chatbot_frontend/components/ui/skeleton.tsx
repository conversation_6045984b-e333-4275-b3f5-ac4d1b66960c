import { cn } from '@/lib/utils'

function Skeleton({ className, ...props }: React.ComponentProps<'div'>) {
  return <div data-slot="skeleton" className={cn('bg-foreground/10 animate-pulse rounded-md', className)} {...props} />
}

function SkeletonText({
  className,
  size = 'base',
  ...props
}: React.ComponentProps<'div'> & {
  size?: 'xs' | 'sm' | 'base' | 'lg'
}) {
  return (
    <div
      className={cn(
        {
          xs: 'h-4 py-0.5',
          sm: 'h-5 py-[.1875rem]',
          base: 'h-6 py-1',
          lg: 'h-7 py-[.3125rem]',
        }[size],
        '[&_[data-slot="skeleton"]]:h-full',
        className,
      )}
      {...props}
    />
  )
}

export { Skeleton, SkeletonText }
