import { dateTimeToString, dateToString } from '@/lib/date-time'

export function DateTime({
  datetime,
  children,
  className,
  precision = 'day',
}: {
  datetime: Date | string
  children?: React.ReactNode
  className?: string
  precision?: 'day' | 'second'
}) {
  const date = datetime instanceof Date ? datetime : new Date(datetime)
  return (
    <time dateTime={date.toISOString()} className={className} suppressHydrationWarning>
      {children ?? (precision === 'second' ? dateTimeToString : dateToString)(date)}
    </time>
  )
}
