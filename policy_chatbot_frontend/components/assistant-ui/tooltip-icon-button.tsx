'use client'

import { ComponentProps, forwardRef } from 'react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Tooltip } from '@/components/ui/tooltip'

export type TooltipIconButtonProps = ComponentProps<typeof Button> & {
  tooltip: string
  side?: 'top' | 'bottom' | 'left' | 'right'
}

export const TooltipIconButton = forwardRef<HTMLButtonElement, TooltipIconButtonProps>(
  ({ children, tooltip, side = 'bottom', className, ...rest }, ref) => {
    return (
      <Tooltip side={side} tip={tooltip} asChild>
        <Button
          variant="ghost"
          size="icon"
          {...rest}
          className={cn('size-6 p-1 [&_svg]:size-3.5', className)}
          ref={ref}
        >
          {children}
          <span className="sr-only">{tooltip}</span>
        </Button>
      </Tooltip>
    )
  },
)

TooltipIconButton.displayName = 'TooltipIconButton'
