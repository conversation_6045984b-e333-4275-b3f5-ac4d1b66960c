import { ActionBarPrimitive, useMessage } from '@assistant-ui/react'
import { ThumbsDownIcon, ThumbsUpIcon } from 'lucide-react'

import { FeedbackDialog } from '@/components/assistant-ui/feedback-dialog'
import { TooltipIconButton } from '@/components/assistant-ui/tooltip-icon-button'

export function FeedbackTools() {
  const status = useMessage((m) => m.status)
  const submitted = useMessage((m) => m.submittedFeedback)

  return (
    status?.type === 'complete' && (
      <>
        {(!submitted || submitted.type === 'positive') && (
          <ActionBarPrimitive.FeedbackPositive asChild>
            <TooltipIconButton tooltip="喜欢">
              <ThumbsUpIcon fill={submitted?.type === 'positive' ? 'currentColor' : 'none'} />
            </TooltipIconButton>
          </ActionBarPrimitive.FeedbackPositive>
        )}
        {(!submitted || submitted.type === 'negative') && (
          <ActionBarPrimitive.FeedbackNegative asChild>
            <TooltipIconButton tooltip="不喜欢">
              <ThumbsDownIcon fill={submitted?.type === 'negative' ? 'currentColor' : 'none'} />
            </TooltipIconButton>
          </ActionBarPrimitive.FeedbackNegative>
        )}
      </>
    )
  )
}
