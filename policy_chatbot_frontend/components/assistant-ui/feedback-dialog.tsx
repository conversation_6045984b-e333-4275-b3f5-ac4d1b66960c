import { useState } from 'react'
import { useMessage } from '@assistant-ui/react'
import { useMutation } from '@tanstack/react-query'
import { MessageCircleWarning } from 'lucide-react'
import { toast } from 'sonner'

import { sendMessageFeedback } from '@/lib/feedback-adapter'
import { TooltipIconButton } from '@/components/assistant-ui/tooltip-icon-button'
import { Button, PendingButton } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'

export function FeedbackDialog() {
  const [open, setOpen] = useState(false)

  const id = useMessage((m) => m.id)

  const { mutate: sendFeedback, isPending } = useMutation({
    mutationFn: async (content: string) => {
      await sendMessageFeedback({ message_id: id, content })
        .then(() => {
          toast.success('反馈已发送')
          setOpen(false)
        })
        .catch((err: Error) => toast.error(err.message))
    },
  })

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <TooltipIconButton tooltip="反馈">
          <MessageCircleWarning />
        </TooltipIconButton>
      </DialogTrigger>
      <DialogContent asChild>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            const content = (e.currentTarget[0] as HTMLTextAreaElement).value
            if (!content) return
            sendFeedback(content)
          }}
        >
          <DialogHeader>
            <DialogTitle>反馈</DialogTitle>
            <DialogDescription>写下对这条消息的反馈与建议。</DialogDescription>
          </DialogHeader>
          <Textarea />
          <DialogFooter>
            <DialogClose asChild>
              <Button className="rounded-full" variant="outline">
                取消
              </Button>
            </DialogClose>
            <PendingButton className="rounded-full" pending={isPending}>
              发送
            </PendingButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
