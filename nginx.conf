user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    # 启用 TCP Keepalive
    proxy_socket_keepalive on;

    server {
      listen 80;
      server_name localhost;

      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      
      location /api {
	client_max_body_size 100M;
        proxy_pass http://backend:8000;
        proxy_buffering off;
	proxy_read_timeout 1200s;
        
        # 重定向修正
        proxy_redirect ~^(http://backend:8000)(.*)$ http://$host/api$2;
        
        # CORS 头
        add_header 'Access-Control-Allow-Origin' 'http://*************' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
      }

      location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Accept-Encoding "";
      }
    }
}
