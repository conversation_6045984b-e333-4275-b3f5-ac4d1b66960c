from pydantic import AnyHttpUrl, Field, field_validator
from pydantic_core.core_schema import ValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict


class UserConfig(BaseSettings):
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin233"

    model_config = SettingsConfigDict(extra="ignore")


class LLMConfig(BaseSettings):
    """LLM配置类，支持字段级覆盖"""
    API_KEY: str | None = Field(None, description="API密钥，未设置时使用通用配置")
    MODEL: str | None = Field(None, description="模型名称，未设置时使用通用配置")
    API_BASE_URL: AnyHttpUrl | None = Field(None, description="API地址，未设置时使用通用配置")
    TEMPERATURE: float | None = Field(1.0, description="生成温度，未设置时使用通用配置")

    model_config = SettingsConfigDict(extra="ignore")


class Settings(BaseSettings):
    PROJECT_NAME: str = "PolicyChatDemo"

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        extra="ignore"
    )

    ### Only for development environment
    DEBUG_MODE: bool = False

    LOG_TO_FILE: bool = False
    TIMEZONE: str = "Asia/Shanghai"

    # Auth
    AUTH_SECRET_KEY: str = "72081684cf8f91b72932750c8dd83432e2fb78616b3e05de7546bb14c2df04b9"  # 通过 openssl rand -hex 32 生成
    AUTH_ENCODE_ALGORITHM: str = "HS256"
    AUTH_ACCESS_TOKEN_EXPIRE_HOURS: int = 24 * 3
    AUTH_TOKEN_URL: str = "/api/v1/auth/login"

    # 大语言模型相关配置
    LLM_IS_DEEPSEEK_REASONER: bool = False
    # 通用配置（必须配置）
    LLM_KEY: str = Field(..., description="通用API密钥")
    LLM_MODEL: str = Field(..., description="通用模型名称")
    LLM_API_BASE_URL: AnyHttpUrl = Field(..., description="通用API地址")
    LLM_TEMPERATURE: float = Field(1.0, description="通用生成温度")

    # 场景专用配置（可选）
    CHAT_LLM: LLMConfig | None = None
    NORMAL_LLM: LLMConfig | None = None

    # Embedding Config
    EMBEDDING_URL: AnyHttpUrl
    EMBEDDING_KEY: str
    EMBEDDING_MODEL: str
    EMBEDDING_TYPE: str = "float"
    EMBEDDING_DIMENSIONS: int = 1024

    # Postgresql
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str
    DB_USERNAME: str
    DB_PASSWORD: str
    DB_MAX_CONNECTION: int = 30

    # RAG Retrieve
    RAG_RETRIEVE_TOP_K: int = 5
    RAG_RETRIEVE_SCALE_FACTOR: float = 1.
    # 检索到的文档chunk数大于3时，回溯文档全部内容，过长时则文档内按照top_K数量检索
    RAG_RETRIEVE_ALL_THRESHOLD_CHUNK_COUNT: int = 3
    # 回溯文档全部内容时，限制最大字符数，超过后文档内检索 (单位：字，不是token)
    RAG_RETRIEVE_ALL_MAX_WORDS: int = 5000

    # Rerank
    RERANK_MODEL: str
    RERANK_URL: AnyHttpUrl
    RERANK_KEY: str = ""

    # User
    user_config: UserConfig = UserConfig()

    @field_validator("CHAT_LLM", "NORMAL_LLM", mode="after")
    @classmethod
    def inject_common_config(cls, llm_config: LLMConfig | None, info: ValidationInfo) -> LLMConfig:
        """融合通用配置到场景配置"""
        if llm_config is None:
            llm_config = LLMConfig(
                API_KEY=info.data.get("LLM_KEY"),
                MODEL=info.data.get("LLM_MODEL"),
                API_BASE_URL=info.data.get("LLM_API_BASE_URL"),
                TEMPERATURE=info.data.get("LLM_TEMPERATURE")
            )

        # 获取通用配置值
        common_config = {
            "API_KEY": info.data.get("LLM_KEY"),
            "MODEL": info.data.get("LLM_MODEL"),
            "API_BASE_URL": info.data.get("LLM_API_BASE_URL"),
            "TEMPERATURE": info.data.get("LLM_TEMPERATURE")
        }

        # 填充未设置的字段
        for field in common_config:
            if getattr(llm_config, field) is None:
                setattr(llm_config, field, common_config[field])

        return llm_config


settings = Settings()
