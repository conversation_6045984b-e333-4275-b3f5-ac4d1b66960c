import asyncio
from apps.shared.document_retriever import DocumentRetriever, RetrieveStepResult
from apps.shared.repository.policy import (
    PolicyChunkRepository,
    DocumentFilterParams,
    PolicyDocumentRepository,
)
from apps.shared.model.policy import PolicyDocument
from test import init_test_env
from core.rag.embedding import embedding_client
from peewee import SQL
from core.database import db
from apps.shared.schema.policy import DocumentUpdate

policy_chunk_repo = PolicyChunkRepository()
document_repo = PolicyDocumentRepository()


@init_test_env()
async def test():
    retriever = DocumentRetriever(policy_chunk_repo)
    query = "教学鉴定内容"
    # 执行检索
    result = retriever.rag_retrieve_document(query=query, context=[])
    async for step, value in result:
        print(step, "=>", value)


asyncio.new_event_loop().run_until_complete(test())
