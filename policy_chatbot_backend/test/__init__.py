import asyncio
import inspect
from functools import wraps

from core.async_utils import shutdown_executors, init_executors
from core.database import initialize_database, shutdown_database
from core.logger import init_logger


def run_async_main(future):
    asyncio.set_event_loop(asyncio.new_event_loop())
    asyncio.get_event_loop().run_until_complete(future)


def init_test_env(executors=True, database=True):  # 外层接收配置参数
    def decorator(func):  # 中层接收被装饰函数
        if inspect.iscoroutinefunction(func):

            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                init_logger()
                if executors:
                    init_executors()
                if database:
                    initialize_database()
                try:
                    return await func(*args, **kwargs)
                finally:
                    if database:
                        shutdown_database()
                    if executors:
                        shutdown_executors()

            return async_wrapper
        else:

            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                init_logger()
                if executors:
                    init_executors()
                if database:
                    initialize_database()
                try:
                    return func(*args, **kwargs)
                finally:
                    if database:
                        shutdown_database()
                    if executors:
                        shutdown_executors()

            return sync_wrapper

    return decorator  # 返回中层装饰器
