from apps.chat.schemas import DataStreamProtocol, ClientMessage
from test import init_test_env, run_async_main


@init_test_env(database=False)
async def main():
    print(
        ClientMessage.from_data_stream_protocol(
            """9:{"toolCallId": "1ca6e6cd-c5de-4595-86ba-35257a33df15", "toolName": "decide_action", "args": "\u5bbf\u820d\u91cc\u53ef\u4ee5\u5403\u897f\u74dc\u5417"}
a:{"toolCallId": "1ca6e6cd-c5de-4595-86ba-35257a33df15", "result": "Finished"}
9:{"toolCallId": "3e228472-5e8b-46b3-831d-68ad6b79cd31", "toolName": "retrieve_document", "args": "\u9ad8\u6821\u5b66\u751f\u5bbf\u820d\u7ba1\u7406\u89c4\u5b9a\u4e2d\u5173\u4e8e\u98df\u7269\u6444\u5165\u7684\u76f8\u5173\u653f\u7b56\u6709\u54ea\u4e9b\uff1f"}
a:{"toolCallId": "3e228472-5e8b-46b3-831d-68ad6b79cd31", "result": "\u6587\u6863\u68c0\u7d22\u5b8c\u6210"}
9:{"toolCallId": "0044ce9c-9ce5-4d55-888e-2981d649dada", "toolName": "reasoning_content", "args": {}}
b:{"toolCallId": "0044ce9c-9ce5-4d55-888e-2981d649dada", "toolName": "reasoning_content"}
c:{"toolCallId": "0044ce9c-9ce5-4d55-888e-2981d649dada", "argsTextDelta": "\u597d\u7684"}
c:{"toolCallId": "0044ce9c-9ce5-4d55-888e-2981d649dada", "argsTextDelta": "\u3002"}
0:"\u540c\u5b66"
0:"\u7ef4\u62a4"
0:"\u5c45\u4f4f"
0:"\u73af\u5883"
0:"\u3002"
9:{"toolCallId": "acf8f1d5-01fd-4708-970d-8c5a21f2fc92", "toolName": "generate_references", "args": {}}
a:{"toolCallId": "acf8f1d5-01fd-4708-970d-8c5a21f2fc92", "result": [{"id": 1, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u7814\u7a76\u751f\u4f4f\u5bbf\u7ba1\u7406\u89c4\u5b9a\uff08\u8bd5\u884c\uff09   \uff08\u897f\u7535\u7814\u30142020\u301526 \u53f7\uff09"}, {"id": 2, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u5c11\u6570\u6c11\u65cf\u9884\u79d1\u751f\u7efc\u5408\u6d4b\u8bc4\u7ec6\u5219   \uff082025 \u5e74 02 \u6708 21 \u65e5\uff09"}, {"id": 3, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u201c\u56db\u597d\u201d\u5bbf\u820d\u8bc4\u5b9a\u7ba1\u7406\u529e\u6cd5   \uff082020\u5e7411\u670818\u65e5\uff09"}, {"id": 4, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u672c\u79d1\u751f\u7ba1\u7406\u89c4\u5b9a   \uff08\u897f\u7535\u5b66\u30142017\u301512 \u53f7\uff09"}, {"id": 5, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u4e66\u9662\u548c\u5b66\u9662\u5171\u540c\u80b2\u4eba\u5b9e\u65bd\u7ec6\u5219\uff08\u8bd5\u884c\uff09   \uff08\u897f\u7535\u53d1\u30142020\u30151 \u53f7\uff09"}, {"id": 6, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u8282\u80fd\u7ba1\u7406\u529e\u6cd5\uff08\u6682\u884c\uff09   \uff08\u897f\u7535\u540e\u30142022\u30156 \u53f7\uff09"}, {"id": 7, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u5b66\u751f\u516c\u5bd3\u7ba1\u7406\u89c4\u5b9a   \uff08\u897f\u7535\u540e\u30142011\u30155 \u53f7\uff09"}, {"id": 8, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u672c\u79d1\u751f\u8fdd\u7eaa\u5904\u5206\u529e\u6cd5   \uff08\u897f\u7535\u5b66\u30142020\u301532 \u53f7\uff09"}, {"id": 9, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u98df\u54c1\u539f\u6750\u6599\u91c7\u8d2d\u7ba1\u7406\u529e\u6cd5\uff08\u8bd5\u884c\uff09   \uff082021\u5e743\u67089\u65e5\uff09"}, {"id": 10, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u5b66\u751f\u515a\u652f\u90e8\u5de5\u4f5c\u6807\u51c6   \uff08\u897f\u7535\u515a\u7ec4\u30142021\u301537 \u53f7\uff09"}, {"id": 11, "title": "\u897f\u5b89\u7535\u5b50\u79d1\u6280\u5927\u5b66\u515a\u653f\u673a\u5173\u516c\u6587\u5904\u7406\u529e\u6cd5 \uff08\u897f\u7535\u53d1\u30142013\u301511 \u53f7\uff09"}]}
d:{"finishReason": "stop", "usage": {"promptTokens": 0, "completionTokens": 0}}"""
        ).content
    )


run_async_main(main())
