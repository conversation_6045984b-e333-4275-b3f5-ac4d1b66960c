import asyncio
import uuid

from apps.chat.chat_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from apps.shared.repository.chat import ChatThreadRepository, ChatMessageRepository
from test import init_test_env


@init_test_env()
async def test_chat():
    chat_manager = ChatManager(
        thread_repo=ChatThreadRepository(), message_repo=ChatMessageRepository()
    )
    thread = await chat_manager.start_new_thread(str(uuid.uuid4()), user_id=1000)
    root_message = await thread.start_message(str(uuid.uuid4()), "user", "Hello")
    second_message = await root_message.append_message(
        str(uuid.uuid4()), "assistant", "caillo"
    )
    print(second_message)


loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)
loop.run_until_complete(test_chat())
