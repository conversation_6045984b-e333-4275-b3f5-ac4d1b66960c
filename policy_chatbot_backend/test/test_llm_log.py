from core.async_utils import init_executors, shutdown_executors
from core.database import initialize_database, shutdown_database
from core.agents.base import record_llm_invoke
from test import run_async_main

init_executors()
initialize_database()

run_async_main(record_llm_invoke(
    function="test_func",
    model="idiot-llm",
    prompt="123{query}",
    params={"query": "666"},
    response="777"
))

shutdown_database()
shutdown_executors()
