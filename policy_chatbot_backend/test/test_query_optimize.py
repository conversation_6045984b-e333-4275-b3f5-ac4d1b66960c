from apps.chat.schemas import ClientMessage
from core.agents.query_optimize import stepback_document_query, decompose_document_query
from core.logger import init_logger
from test import run_async_main, init_test_env

messages = [
    ClientMessage(
        role="system",
        content="您是一个专业的政策咨询助手，帮助用户解答与政策相关的疑问。",
    ),
    ClientMessage(role="user", content="您好，我想了解一下最新的税收优惠政策。"),
    ClientMessage(
        role="assistant",
        content="您好，根据最新政策，小微企业和个体工商户可以享受增值税减免，具体细则请参阅财政部官方网站。",
    ),
    ClientMessage(role="user", content="请问这些优惠政策的申请条件是什么？"),
]


@init_test_env()
async def main():
    init_logger()
    question = messages[-1].content

    stepback_query = await stepback_document_query(
        query=question, context=str(messages)
    )

    print(stepback_query)

    subqueries = await decompose_document_query(stepback_query)
    print(subqueries)


run_async_main(main())
