from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI

from settings import settings
from test import run_async_main, init_test_env

_agent_model = ChatDeepSeek(
    api_base=str(settings.LLM_API_BASE_URL),
    api_key=settings.LLM_KEY,
    model="deepseek-r1",
    temperature=settings.LLM_TEMPERATURE,
)


@init_test_env(database=False)
async def main():
    response = _agent_model.invoke("你好")
    print(response)

    # chunks = _agent_model.stream("你好")
    # print(chunks)
    # for chunk in chunks:
    #     if chunk.response_metadata is not None:
    #         print(chunk.response_metadata)
    #     print(type(chunk), ' ', chunk)


run_async_main(main())
