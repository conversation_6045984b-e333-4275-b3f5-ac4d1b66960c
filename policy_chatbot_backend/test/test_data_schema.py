import json
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from apps.chat.schemas import Client<PERSON>ttachment, DataStreamProtocol, ToolInvocation


class ClientMessage(BaseModel):
    unstable_id: Optional[str] = None
    role: str
    content: Any
    experimental_attachments: Optional[List[ClientAttachment]] = None
    toolInvocations: Optional[List[ToolInvocation]] = None
    metadata: Dict[str, Any] = {}
    id: Optional[str] = None
    status: Optional[Dict[str, Any]] = None
    createdAt: Optional[str] = None


def parse_data_stream(data_stream: str) -> ClientMessage:
    """将数据流协议解析为 ClientMessage"""
    message = ClientMessage(role="assistant", content=[], metadata={})
    tool_calls = {}  # 跟踪正在处理的工具调用
    current_steps = []

    for line in data_stream.strip().split("\n"):
        if ":" not in line:
            continue

        prefix, payload = line.split(":", 1)
        data = json.loads(payload)

        # 文本内容
        if prefix == "0":
            message.content.append({"type": "text", "text": data})

        # 工具调用处理
        elif prefix == "b":
            tool_call = {
                "type": "tool-call",
                "toolCallId": data["toolCallId"],
                "toolName": data["toolName"],
                "argsText": "",
                "args": {},
                "result": None,
            }
            tool_calls[data["toolCallId"]] = tool_call

        elif prefix == "c":
            if data["toolCallId"] in tool_calls:
                tool_calls[data["toolCallId"]]["argsText"] += data["argsTextDelta"]

        elif prefix == "9":
            if data["toolCallId"] in tool_calls:
                tool_call = tool_calls.pop(data["toolCallId"])
                tool_call.update(
                    {"args": data.get("args", {}), "argsText": data.get("argsText", "")}
                )
                message.content.append(tool_call)

        elif prefix == "a":
            for item in message.content:
                if item.get("toolCallId") == data["toolCallId"]:
                    item["result"] = data["result"]

        # 元数据处理
        elif prefix == "f":
            current_steps.append({"usage": {}})

        elif prefix == "e":
            if current_steps:
                current_steps[-1].update(
                    {"finishReason": data["finishReason"], "usage": data["usage"]}
                )

        elif prefix == "d":
            message.status = {"type": "complete", "reason": data["finishReason"]}
            message.metadata.setdefault("steps", []).extend(current_steps)
            current_steps = []

    return message


def generate_data_stream(message: ClientMessage) -> str:
    """将 ClientMessage 转换为数据流协议"""
    stream = []

    # 处理内容部分
    for item in message.content:
        # 文本内容
        if item.get("type") == "text":
            stream.append(DataStreamProtocol.text(item["text"]))

        # 工具调用
        elif item.get("type") == "tool-call":
            # 工具调用开始
            stream.append(
                DataStreamProtocol.tool_call_start(
                    item["toolCallId"], item["toolName"]
                )
            )

            # 参数增量（假设全量发送）
            if item.get("argsText"):
                stream.append(
                    DataStreamProtocol.tool_call_delta(
                        item["toolCallId"], item["argsText"]
                    )
                )

            # 完整调用参数
            stream.append(
                DataStreamProtocol.tool_call(
                    item["toolCallId"], item["toolName"], item.get("args", {})
                )
            )

            # 工具调用结果
            if item.get("result") is not None:
                stream.append(
                    DataStreamProtocol.tool_result(
                        item["toolCallId"], item["result"]
                    )
                )

    # 处理元数据
    if message.metadata.get("steps"):
        for step in message.metadata["steps"]:
            stream.append(DataStreamProtocol.start_step(""))
            stream.append(
                DataStreamProtocol.finish_step(
                    step.get("finishReason", "stop"), step.get("usage", {}), False
                )
            )

    # 添加结束标记
    if message.status:
        stream.append(
            DataStreamProtocol.finish_message(
                message.status.get("reason", "stop"), message.metadata.get("usage", {})
            )
        )

    return "".join(stream)


# 使用示例
if __name__ == "__main__":
    # 示例数据流输入
    sample_stream = (
        'b:{"toolCallId": "call1", "toolName": "search"}\n'
        'c:{"toolCallId": "call1", "argsTextDelta": "query"}\n'
        '9:{"toolCallId": "call1", "toolName": "search", "args": {"query": "test"}}\n'
        'a:{"toolCallId": "call1", "result": {"data": 123}}\n'
        '0:"Hello World"\n'
        'd:{"finishReason": "stop", "usage": {}}\n'
    )

    # 解析测试
    parsed_message = parse_data_stream(sample_stream)
    print("Parsed Message:", parsed_message.dict())

    # 生成测试
    generated_stream = generate_data_stream(parsed_message)
    print("\nGenerated Stream:")
    print(generated_stream)
