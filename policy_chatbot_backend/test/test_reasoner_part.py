from apps.chat.schemas import ReasoningContentPart


def test_reasoner_generator():
    part = ReasoningContentPart("test", "reasoning_content")
    yield (part.start())
    yield (part.delta("test"))
    yield (part.delta("2"))
    yield (part.delta("3"))
    yield (part.delta("3"))
    yield (part.delta("3"))
    yield (part.finish())


if __name__ == "__main__":
    for chunk in test_reasoner_generator():
        print(chunk)
