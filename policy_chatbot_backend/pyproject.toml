[project]
name = "policy-chatbot-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "chonkie>=0.5.0",
    "fastapi>=0.115.8",
    "jsonschema>=4.23.0",
    "langchain[openai]>=0.3.19",
    "langchain-text-splitters>=0.3.6",
    "openai>=1.63.2",
    "peewee>=3.17.9",
    "pgvector>=0.3.6",
    "psycopg2-binary>=2.9.10",
    "pydantic-settings>=2.8.0",
    "requests>=2.32.3",
    "streamlit>=1.42.2",
    "tiktoken>=0.9.0",
    "uvicorn>=0.34.0",
    "langchain-deepseek>=0.1.2",
    "pwdlib[bcrypt]>=0.2.1",
    "python-jose[cryptography]>=3.4.0",
    "docling>=2.28.0",
    "python-multipart>=0.0.20",
]
