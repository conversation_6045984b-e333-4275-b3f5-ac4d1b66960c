import time
import uuid
from typing import Dict, List, Any, Optional, AsyncGenerator
from uuid import UUID
from pydantic import Field, BaseModel
from enum import Enum
import json

from core.agents.base import LLMChatResponse


class FeedbackCreate(BaseModel):
    message_id: UUID = Field(description="需反馈的大模型消息id,为客户端的unstable_id")
    feedback: str = Field(description="用户反馈内容")


class ClientAttachment(BaseModel):
    name: str
    contentType: str
    url: str


class ToolInvocation(BaseModel):
    toolCallId: str
    toolName: str
    args: dict
    result: dict


_CLIENT_MESSAGE_MAIN_ATTRS = ["unstable_id", "role", "content"]


class ClientMessage(BaseModel):
    unstable_id: Optional[str] = None
    role: str
    content: Any
    experimental_attachments: Optional[List[ClientAttachment]] = None
    toolInvocations: Optional[List[ToolInvocation]] = None
    metadata: Dict[str, Any] = {}
    id: Optional[str] = None
    status: Optional[Dict[str, Any]] = None
    createdAt: Optional[str] = None

    def __repr__(self):
        return self.model_dump().__repr__()

    def to_message_content(self) -> str:
        return f"`{self.role}`: `{self.content}`"

    def get_addition_data(self) -> Dict[str, Any]:
        return {
            k: v for k, v in self.model_dump().items() if k not in _CLIENT_MESSAGE_MAIN_ATTRS
        }

    @staticmethod
    def from_data_stream_protocol(data: str) -> "ClientMessage":
        """将数据流协议解析为 ClientMessage"""
        message = ClientMessage(role="assistant", content=[], metadata={})
        text_content = ""
        tool_calls = {}  # 跟踪正在处理的工具调用
        current_steps = []

        for line in data.strip().split("\n"):
            if ":" not in line:
                continue

            prefix, payload = line.split(":", 1)
            data = json.loads(payload)

            # 文本内容
            if prefix == "0":
                text_content += data

            # 工具调用处理
            elif prefix == "b":
                pass

            elif prefix == "c":
                if data["toolCallId"] in tool_calls:
                    tool_calls[data["toolCallId"]]["argsText"] += data["argsTextDelta"]

            elif prefix == "9":
                tool_call = {
                    "type": "tool-call",
                    "toolCallId": data["toolCallId"],
                    "toolName": data["toolName"],
                    "args": data.get("args", {}),
                    "argsText": data.get("argsText", ""),
                    "result": None,
                }
                tool_calls[data["toolCallId"]] = tool_call

            elif prefix == "a":
                for item in message.content:
                    if item.get("toolCallId") == data["toolCallId"]:
                        item["result"] = data["result"]

            # 元数据处理
            elif prefix == "f":
                current_steps.append({"usage": {}})

            elif prefix == "e":
                if current_steps:
                    current_steps[-1].update(
                        {"finishReason": data["finishReason"], "usage": data["usage"]}
                    )

            elif prefix == "d":
                message.status = {"type": "complete", "reason": data["finishReason"]}
                message.metadata.setdefault("steps", []).extend(current_steps)
                current_steps = []

        message.content.append({"type": "text", "text": text_content})

        for _, tool_call in tool_calls.items():
            message.content.append(tool_call)

        return message


class ChatRequest(BaseModel):
    thread_id: str = Field(..., description="客户端的会话id")
    messages: List[ClientMessage]
    unstable_assistantMessageId: str  # 下一条消息的客户端id


class DataStreamProtocol(Enum):
    """AI SDK 数据流协议生成工具类"""

    @staticmethod
    def text(text: str) -> str:
        """文本部分 格式：0:string"""
        return f"0:{json.dumps(text)}\n"

    @staticmethod
    def reasoning(reasoning: str) -> str:
        """推理部分 格式：g:string"""
        return f"g:{json.dumps(reasoning)}\n"

    @staticmethod
    def redacted_reasoning(data: str) -> str:
        """redacted推理部分 格式：i:{"data": ...}"""
        return f"i:{json.dumps({'data': data})}\n"

    @staticmethod
    def reasoning_signature(signature: str) -> str:
        """推理签名部分 格式：j:{"signature": ...}"""
        return f"j:{json.dumps({'signature': signature})}\n"

    @staticmethod
    def source(source: dict) -> str:
        """来源部分 格式：h:Source"""
        return f"h:{json.dumps(source)}\n"

    @staticmethod
    def data(data: list) -> str:
        """数据部分 格式：2:Array<JSONValue>"""
        return f"2:{json.dumps(data)}\n"

    @staticmethod
    def annotation(annotations: list) -> str:
        """消息注释部分 格式：8:Array<JSONValue>"""
        return f"8:{json.dumps(annotations)}\n"

    @staticmethod
    def error(error_message: str) -> str:
        """错误部分 格式：3:string"""
        return f"3:{json.dumps(error_message)}\n"

    @staticmethod
    def tool_call_start(tool_call_id: str | UUID, tool_name: str) -> str:
        """工具调用流开始部分 格式：b:{toolCallId, toolName}"""
        if isinstance(tool_call_id, UUID):
            tool_call_id = str(tool_call_id)
        return f"b:{json.dumps({'toolCallId': tool_call_id, 'toolName': tool_name})}\n"

    @staticmethod
    def tool_call_delta(tool_call_id: str | UUID, delta: str) -> str:
        """工具调用增量部分 格式：c:{toolCallId, argsTextDelta}"""
        if isinstance(tool_call_id, UUID):
            tool_call_id = str(tool_call_id)
        return f"c:{json.dumps({'toolCallId': tool_call_id, 'argsTextDelta': delta})}\n"

    @staticmethod
    def tool_call(tool_call_id: str | UUID, tool_name: str, args: Any) -> str:
        """工具调用部分 格式：9:{toolCallId, toolName, args}"""
        if isinstance(tool_call_id, UUID):
            tool_call_id = str(tool_call_id)
        return f"9:{json.dumps({'toolCallId': tool_call_id, 'toolName': tool_name, 'args': args})}\n"

    @staticmethod
    def tool_result(tool_call_id: str | UUID, result: Any) -> str:
        """工具结果部分 格式：a:{toolCallId, result}"""
        if isinstance(tool_call_id, UUID):
            tool_call_id = str(tool_call_id)
        return f"a:{json.dumps({'toolCallId': tool_call_id, 'result': result})}\n"

    @staticmethod
    def start_step(message_id: str) -> str:
        """开始步骤部分 格式：f:{messageId}"""
        return f"f:{json.dumps({'messageId': message_id})}\n"

    @staticmethod
    def finish_step(finish_reason: str, usage: dict, is_continued: bool) -> str:
        """完成步骤部分 格式：e:{finishReason, usage, isContinued}"""
        return f"e:{
        json.dumps(
            {
                'finishReason': finish_reason,
                'usage': usage,
                'isContinued': is_continued,
            }
        )
        }\n"

    @staticmethod
    def finish_message(finish_reason: str, usage: dict) -> str:
        """完成消息部分 格式：d:{finishReason, usage}"""
        return f"d:{json.dumps({'finishReason': finish_reason, 'usage': usage})}\n"


class ToolCallPart:
    """工具调用部分的操作封装，用于管理同一工具调用的多个步骤"""

    def __init__(self, tool_call_id: str, tool_name: str):
        self.tool_call_id = tool_call_id
        self.tool_name = tool_name

    def result(self, result: Any) -> str:
        """生成工具调用结果的协议字符串"""
        return DataStreamProtocol.tool_result(self.tool_call_id, result)

    def call(self, args: Any = {}) -> str:
        """生成完整工具调用的协议字符串"""
        return DataStreamProtocol.tool_call(self.tool_call_id, self.tool_name, args)


class ToolCallDeltaPart:
    def __init__(self, tool_call_id: str, tool_name: str):
        self.tool_call_id = tool_call_id
        self.tool_name = tool_name
        self.started = False
        self.final_delta_result = ""

    def call(self, args: Any = {}) -> str:
        """生成完整工具调用的协议字符串"""
        return DataStreamProtocol.tool_call(self.tool_call_id, self.tool_name, args)

    def start(self) -> str:
        self.started = True
        """生成工具调用开始的协议字符串"""
        return DataStreamProtocol.tool_call_start(self.tool_call_id, self.tool_name)

    def delta(self, delta: str) -> str:
        """生成工具调用增量参数的协议字符串"""
        self.final_delta_result += delta
        return DataStreamProtocol.tool_call_delta(self.tool_call_id, delta)

    def result(self, content: str = None) -> str:
        """生成工具调用结果的协议字符串"""
        if content is None:
            content = self.final_delta_result
        return DataStreamProtocol.tool_result(self.tool_call_id, content)


class MessageStepPart:
    """消息步骤操作封装"""

    def __init__(self, message_id: str):
        self.message_id = message_id

    def start(self) -> str:
        """生成步骤开始协议"""
        return DataStreamProtocol.start_step(self.message_id)

    def finish(self, finish_reason: str, usage: dict, is_continued: bool) -> str:
        """生成步骤结束协议"""
        return DataStreamProtocol.finish_step(finish_reason, usage, is_continued)


class ReasoningPart:
    """推理流程操作封装"""

    def __init__(self, reasoning_id: str):
        self.reasoning_id = reasoning_id

    def begin(self, reasoning: str) -> str:
        """生成基础推理协议"""
        return DataStreamProtocol.reasoning(f"[{self.reasoning_id}] {reasoning}")

    def redacted(self, data: str) -> str:
        """生成脱敏推理协议"""
        return DataStreamProtocol.redacted_reasoning(f"[{self.reasoning_id}] {data}")

    def signature(self, signature: str) -> str:
        """生成推理签名协议"""
        return DataStreamProtocol.reasoning_signature(
            f"[{self.reasoning_id}] {signature}"
        )


class ReasoningContentPart(ToolCallDeltaPart):

    def __init__(self, tool_call_id: str, tool_name: str):
        super().__init__(tool_call_id, tool_name)
        self.finished: bool = False
        self.start_time = time.time()

    def finish(self) -> str:
        self.finished = True
        return self.result(content=f"{round(time.time() - self.start_time)}")


class ChatDeltaPart:

    def __init__(self):
        self.reason_part = ReasoningContentPart(str(uuid.uuid4()), "reasoning_content")

    async def message(self, chat_response: AsyncGenerator[LLMChatResponse, None]) -> AsyncGenerator[str, None]:
        async for response in chat_response:
            if response.is_reansoning:
                if not self.reason_part.started:
                    yield self.reason_part.start()

                yield self.reason_part.delta(response.reasoning_content)
            else:
                if response.content is None:
                    continue

                # 当有content时，reasoning content必结束
                if not self.reason_part.finished:
                    yield self.reason_part.finish()

                yield DataStreamProtocol.text(response.content)
