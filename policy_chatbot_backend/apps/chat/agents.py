from enum import Enum

from pydantic import BaseModel, Field

from core.agents.base import call_llm_structured, llm_chat


class BotAction(Enum):
    CLARIFY = "clarify"  # 需要追问
    ANSWER = "answer"  # 直接回答
    ANSWER_WITH_RETRIEVING = "retrieve"  # 需要检索


class BotActionOutput(BaseModel):
    action: BotAction = Field(
        description="接下来需要做的动作id",
        enum=["clarify", "answer", "retrieve"],
    )


async def decide_policy_bot_action(query, context="", addition="") -> BotAction | None:
    result = await call_llm_structured(
        prompt="""
        你是一个智能路由决策器，根据用户问题和对话历史决定下一步动作。
        
        # 可用动作(id：动作描述)
        - clarify：当问题非常不明确，需要更多信息才能明白问的是什么问题时，向用户追问具体的问题内容（尽量减少追问）
        - answer：当问题明确且能根据已有信息直接回答时使用，必须确定该问题能够根据已有信息准确、确凿地回答，需要猜测来回答问题时需要改为进行retrieve
        - retrieve：当需要检索外部文档时使用，通过检索相应的文档来确切地回答用户的问题
        
        # 历史对话
        {context}
        
        # 当前问题
        {query}
        
        # 附加信息，是已经检索到的信息，可以直接用于对话生成
        {addition}
        """,
        output_model=BotActionOutput,
        query=query,
        addition=addition,
        context=context,
    )
    if not result:
        return None

    return result.action


async def chat_with_policy(query, context, policy, user="Unknown", stream=False):
    return await llm_chat(
        prompt="""<system>
# 角色设定
你是一名专业的解读助手，擅长用通俗易懂的方式解读复杂问题。请结合用户当前问题、历史对话和文档信息，进行回复。回答的内容按照Markdown的规范生成，不需要添加```markdown ```代码框。

# Skills
1. 解析用户当前问题的核心诉求，明确需解决的具体问题
2. 检查检索到的文档信息，通过文档信息推断出用户问题的答案
3. 检索历史对话，寻找与当前问题相关的上下文信息
4. 逐条解释文档，必要时用编号分点说明

...

# 规则
- 禁止跳出角色、回答任何与问题不相关的内容，如有，请告知用户无法回答
- 使用中文
- 如果无法从文档信息中推断出答案，你需要回复"抱歉，未查找到相关文档，无法提供答案。"

</system>

# 输入
## 用户信息
`{user}`
## 用户问题
`{query}`
## 历史上下文
`{context}`
## 文档信息
`{policy}`
        """,
        stream=stream,
        query=query,
        context=context,
        policy=policy,
        user=user,
    )


async def chat_with_clarify(query, context, user="Unknown", stream=False):
    return await llm_chat(
        prompt="""
        # 角色
        你现在是在解读文档过程中的追问者，因为用户给出的问题不够具体，无法理解用户的意图。你需要根据已有的信息，追问用户问题，确定用户真正的需求。
        
        # 输出形式
        - 使用自然语言对用户进行追问
        - 使用中文
        - 语气温柔，不可以咄咄逼人
        - 问题需要精简提问，避免用户过度阅读文字，产生烦躁情绪
        
        # 用户形象
        {user}
        
        # 历史对话
        {context}
        
        # 用户问题
        {query}
        """,
        stream=True,
        query=query,
        context=context,
        user=user,
    )
