import json
from typing import AsyncGenerator, List
from fastapi import APIRouter, Body
from fastapi.params import Depends
from fastapi.responses import StreamingResponse

from apps.auth.auth import get_current_user
from apps.auth.user_manager import UserData
from apps.chat.policy_chat_bot import PolicyChatBot
from apps.chat.repository import FeedbackRepository
from apps.chat.schemas import ChatRequest, ClientMessage, FeedbackCreate, DataStreamProtocol
from apps.chat.chat_manager import Chat<PERSON>anager
from apps.shared.depends import get_chat_manager_dep, get_document_retriever_dep
from apps.shared.document_retriever import DocumentRetriever
from core.depends import get_repository_dep
import traceback
from fastapi.exceptions import HTTPException

from peewee import DoesNotExist, IntegrityError

from core.logger import get_logger

logger = get_logger(__name__)
chat_router = APIRouter(tags=["chat"])


@chat_router.post("/chat/feedback", )
async def create_feedback(
        message_id: str = Body(..., description="消息id, 为客户端的unstable_id"),
        content: str = Body(..., description="用户反馈内容"),
        user: UserData = Depends(get_current_user),
        repo: FeedbackRepository = get_repository_dep(FeedbackRepository),
        chat_manager: ChatManager = get_chat_manager_dep(),
):
    try:
        message = await chat_manager.get_message(client_message_id=message_id)

        if not (message.thread_wrapper.chat_thread.user_id == user.user_id):
            raise HTTPException(
                status_code=403,
                detail=f"message({message_id}) not belong to user({user.user_id})",
            )

        await repo.create(
            FeedbackCreate(
                message_id=message.chat_message.message_id,
                feedback=content,
            )
        )
        return {"message": "success"}
    except DoesNotExist as _:
        raise HTTPException(status_code=404, detail=f"message({message_id}) not found")
    except Exception as e:
        logger.error("%s\n%s", e, traceback.format_exc())
        raise HTTPException(status_code=500, detail="反馈创建失败")


async def stream_chat(
        context: List[ClientMessage],
        save_message_func,
        document_retriever: DocumentRetriever,
        user: UserData
) -> AsyncGenerator[str]:
    """
    obey Vercel Data Stream Protocol

    Return stream response and store the answer in chat history (ChatThread and ChatMessage).
    """
    query = context[-1].content
    policy_chat_bot = PolicyChatBot(
        document_retriever=document_retriever,
        query_context=context[:-1],
        query=query,
        user=user
    )
    async for chunk in policy_chat_bot.chat():
        yield chunk

    final_message = policy_chat_bot.get_final_message()
    await save_message_func(final_message)


# TODO 把所有的router都显示出包括错误返回的response model
@chat_router.post("/chat", tags=['chat'])
async def handle_chat_request(
        request: ChatRequest,
        user: UserData = Depends(get_current_user),
        chat_manager: ChatManager = get_chat_manager_dep(),
        document_retriever: DocumentRetriever = get_document_retriever_dep(),
):
    # TODO 抽离到chat_service里
    # 尝试获取或创建Thread
    try:
        thread = await chat_manager.get_thread(client_thread_id=request.thread_id)
    except Exception:
        if len(request.messages) == 1:
            try:
                thread = await chat_manager.start_new_thread(
                    client_thread_id=request.thread_id, user_id=user.user_id
                )
            except Exception:
                raise HTTPException(
                    status_code=500,
                    detail=f"Chat thread({request.thread_id}) can't be created.",
                )
        else:
            logger.error(f"Chat thread({request.thread_id}) not found")
            logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=404,
                detail=f"Chat thread({request.thread_id}) not found",
            )

    if await thread.get_belonged_user_id() != user.user_id:
        logger.debug(
            f"Chat thread({request.thread_id}) with user_id={await thread.get_belonged_user_id()} not belong to user({user.user_id})")
        raise HTTPException(
            status_code=403,
            detail=f"Chat thread({request.thread_id}) not belong to user({user.user_id})",
        )

    # 保存用户消息
    last_user_message = request.messages[-1]
    last_user_message_client_id = last_user_message.unstable_id
    last_user_message_content = json.dumps(last_user_message.content, ensure_ascii=False)
    last_user_message_addition_data = last_user_message.get_addition_data()
    flag_update_last_user_message = False
    try:
        if last_user_message_client_id is None:
            raise HTTPException(
                status_code=400,
                detail=f"Message({last_user_message.unstable_id}) unstable_id is required.",
            )
        last_message_wrapper = await thread.append_message(
            client_message_id=last_user_message_client_id,
            role=last_user_message.role,
            content=last_user_message_content,
            addition_data=last_user_message_addition_data,
        )
    except IntegrityError:
        flag_update_last_user_message = True

    # 需要更新最新的用户消息
    if flag_update_last_user_message:
        try:
            last_message_wrapper = await thread.get_message(client_message_id=last_user_message_client_id)
            last_message_wrapper = await last_message_wrapper.update(
                content=last_user_message_content,
                addition_data=last_user_message_addition_data,
            )
        except Exception as e:
            logger.error("%s\n%s", e, traceback.format_exc())
            raise HTTPException(
                status_code=500,
                detail=f"Update last user message error: Message({last_user_message_client_id})",
            )

    # 保存AI回复的闭包（已经包含在事务中）
    async def save_message_func(message: ClientMessage):
        try:
            await last_message_wrapper.append_message(
                client_message_id=request.unstable_assistantMessageId,
                role=message.role,
                content=json.dumps(message.content, ensure_ascii=False),
                addition_data=message.get_addition_data(),
            )
        except IntegrityError as e:
            raise IntegrityError(f"Message{request.unstable_assistantMessageId} already exists.")
        except Exception as e:
            logger.error("Save message error: %s\n%s", e, traceback.format_exc())
            raise Exception(f"Save message error: Message({request.unstable_assistantMessageId}) {e}")

    # 生成流式响应（关键：在事务内完成所有数据库操作）
    # 从生成开始，Http API已经响应 200 OK，因此需要通过 StreamingResponse 返回流式响应异常
    async def generate_stream():
        try:
            async for chunk in stream_chat(
                    context=request.messages,
                    save_message_func=save_message_func,
                    document_retriever=document_retriever,
                    user=user
            ):
                yield chunk
        except Exception as e:
            logger.error("%s\n%s", e, traceback.format_exc())
            yield DataStreamProtocol.error(error_message=f"生成解答时遇到错误：{str(e)}")

    # 返回流式响应前提交事务
    stream = generate_stream()
    response = StreamingResponse(
        stream,
        headers={"x-vercel-ai-data-stream": "v1"},
        media_type="text/plain; charset=utf-8",
    )

    return response
