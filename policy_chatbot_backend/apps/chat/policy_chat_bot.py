import dataclasses
from enum import Enum
import functools
import traceback
from typing import AsyncGenerator, Callable, List, Optional
import uuid

from apps.auth.user_manager import UserData
from apps.chat.agents import (
    BotAction,
    decide_policy_bot_action,
    chat_with_clarify,
    chat_with_policy,
)
from apps.chat.schemas import ClientMessage, DataStreamProtocol, ToolCallPart, ChatDeltaPart
from apps.shared.document_retriever import DocumentRetriever, RetrieveStepResult
from apps.shared.schema.document_chunk import ChunkText, convert_chunk_references
from core.format import dict_to_markdown
from core.logger import get_logger

logger = get_logger(__name__)


class ChatTool(Enum):
    RETRIEVE_DOCUMENT = "retrieve_document"
    GENERATE_REFERENCES = "generate_references"
    DECIDE_ACTION = "decide_action"


@dataclasses.dataclass
class PolicyChatBot:
    query_context: List[ClientMessage]  # 此对话的历史上下文，不含当前的query
    query: str  # 用户最新发送的查询语句
    user: UserData

    document_retriever: DocumentRetriever = None
    _data_stream_response: str = None

    def __post_init__(self):
        # TODO 优化上下文修剪策略
        self.query_context = self.query_context[-6:]

    def collect_chat_response(self: Callable[..., AsyncGenerator[str, None]]):
        @functools.wraps(self)
        async def wrapper(func, *args, **kwargs):
            func._data_stream_response = ""
            gen = self(func, *args, **kwargs)

            async for chunk in gen:
                # 收集每个流式响应块
                func._data_stream_response += chunk
                yield chunk

        return wrapper

    async def _route_action(self) -> BotAction:
        return await decide_policy_bot_action(
            query=self.query, context=self.query_context
        )

    # TODO 优化客户端Tool传入的已有检索结果显示，目前会被context给过滤掉一部分，考虑做为已有policy传入
    @collect_chat_response
    async def chat(self) -> AsyncGenerator[str, None]:
        try:
            route_action_call = ToolCallPart(
                tool_call_id=str(uuid.uuid4()), tool_name=ChatTool.DECIDE_ACTION.value
            )
            yield route_action_call.call(self.query)
            action = await self._route_action()
            yield route_action_call.result("Finished")

            chat_delta_part = ChatDeltaPart()

            # 动作路由
            if action == BotAction.CLARIFY:
                response = await chat_with_clarify(
                    query=self.query,
                    context=self.query_context,
                    stream=True,
                )
                if not response:
                    yield DataStreamProtocol.error("追问时，大模型无响应")
                    return
                async for chunk in chat_delta_part.message(response):
                    yield chunk

            elif action == BotAction.ANSWER:
                response = await chat_with_policy(
                    query=self.query,
                    context=self.query_context,
                    policy="",  # TODO 读取先前获取的政策信息
                    stream=True,
                )
                if not response:
                    yield DataStreamProtocol.error("解答时，大模型无响应")
                    return
                async for chunk in chat_delta_part.message(response):
                    yield chunk

            elif action == BotAction.ANSWER_WITH_RETRIEVING:
                retrieve_tool_call = ToolCallPart(
                    tool_call_id=str(uuid.uuid4()),
                    tool_name=ChatTool.RETRIEVE_DOCUMENT.value,
                )

                retrieve_step_result = self.document_retriever.rag_retrieve_document(
                    query=self.query, context=self.query_context
                )
                if not retrieve_step_result:
                    yield DataStreamProtocol.error("文档检索失败")
                    return

                retrieved_raw_policies: Optional[List[ChunkText]] = None
                async for step, result in retrieve_step_result:
                    if step is RetrieveStepResult.ERROR:
                        yield DataStreamProtocol.error(f"文档检索失败:{result}")
                        return
                    elif step is RetrieveStepResult.OPTIMIZE_QUERY:
                        yield retrieve_tool_call.call(result)
                        pass
                    elif step is RetrieveStepResult.RETRIEVE_DOCUMENT:
                        # yield retrieve_tool_call.call(f"正在多角度检索：{result}")
                        pass
                    elif step is RetrieveStepResult.FINISHED:
                        yield retrieve_tool_call.result("文档检索完成")
                        retrieved_raw_policies = result

                # 处理文档引用
                policies, references = convert_chunk_references(retrieved_raw_policies)
                policy_markdown = [
                    f"{dict_to_markdown(chunk, recursive=False)} \n ---"
                    for chunk in policies
                ]

                answer_response = await chat_with_policy(
                    query=self.query,
                    context=self.query_context,
                    policy=policy_markdown,
                    stream=True,
                )
                if not answer_response:
                    yield DataStreamProtocol.error("解答时，大模型无响应")
                    return
                async for chunk in chat_delta_part.message(answer_response):
                    yield chunk

                references_tool_call = ToolCallPart(
                    tool_call_id=str(uuid.uuid4()),
                    tool_name=ChatTool.GENERATE_REFERENCES.value,
                )
                yield references_tool_call.call()

                # 生成政策依据信息
                # TODO 去掉没有用到的政策依据
                references_data = [
                    {
                        "id": reference.reference_id,
                        "title": reference.reference,
                    }
                    for reference in references
                ]
                yield references_tool_call.result(references_data)

            yield DataStreamProtocol.finish_message(
                finish_reason="stop",
                usage={"promptTokens": 0, "completionTokens": 0},
            )
        except Exception as e:
            logger.error("ChatBot.chat() error: %s\n%s", e, traceback.format_exc())
            yield DataStreamProtocol.error(f"处理请求时发生错误:{e}")
            yield DataStreamProtocol.finish_message(
                finish_reason="error",
                usage={"promptTokens": 0, "completionTokens": 0},
            )

    def get_final_message(self) -> ClientMessage:
        if self._data_stream_response is None:
            logger.warning(
                f"PolicyChatBot._data_stream_response is not initialized. (Final Message = {self.get_final_message()})")
            raise ValueError("PolicyChatBot._data_stream_response is not initialized.")
        return ClientMessage.from_data_stream_protocol(self._data_stream_response)
