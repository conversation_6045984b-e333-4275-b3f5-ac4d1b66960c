import dataclasses
from typing import Any, Dict, List, Optional
from uuid import UUID

from peewee import DoesNotExist

from apps.shared.model.chat import ChatThread, ChatMessage
from apps.shared.repository.chat import ChatThreadRepository, ChatMessageRepository
from apps.shared.schema.chat import ChatMessageCreate, ChatThreadCreate, ChatMessageUpdate


@dataclasses.dataclass
class ChatMessageWrapper:
    thread_wrapper: "ChatThreadWrapper"
    chat_message: ChatMessage

    async def append_message(
            self,
            client_message_id: str,
            role: str,
            content: str,
            addition_data: Optional[Dict[str, Any]] = None,
    ) -> "ChatMessageWrapper":
        return await self.thread_wrapper.new_message(
            client_message_id=client_message_id,
            parent_message_client_id=self.chat_message.client_message_id,
            role=role,
            content=content,
            addition_data=addition_data,
        )

    async def update(self,
                     content: str,
                     addition_data: Optional[Dict[str, Any]] = None) -> "ChatMessageWrapper":
        return await self.thread_wrapper.update_message(
            message_id=self.chat_message.message_id,
            content=content,
            addition_data=addition_data,
        )


@dataclasses.dataclass
class ChatThreadWrapper:
    manager: "ChatManager"
    chat_thread: ChatThread

    async def get_belonged_user_id(self) -> int:
        return self.chat_thread.user_id_id

    async def get_message(
            self, client_message_id: Optional[str] = None, message_id: Optional[UUID] = None
    ) -> ChatMessageWrapper:
        return await self.manager.get_message(
            message_id=message_id, client_message_id=client_message_id
        )

    async def list_messages(self) -> List[ChatMessageWrapper]:
        return await self.manager

    async def start_message(
            self,
            client_message_id: str,
            role: str,
            content: str,
            addition_data: Optional[Dict[str, Any]] = None,
    ) -> ChatMessageWrapper:
        return await self.new_message(
            client_message_id=client_message_id,
            role=role,
            content=content,
            addition_data=addition_data,
        )

    async def new_message(
            self,
            client_message_id: str,
            role: str,
            content: str,
            parent_message_client_id: Optional[str] = None,
            addition_data: Optional[Dict[str, Any]] = {},
    ) -> ChatMessageWrapper:
        if parent_message_client_id is None:
            parent_message_id = None
        else:
            parent_message = await self.get_message(parent_message_client_id)
            parent_message_id = parent_message.chat_message.message_id
        return await self.manager.new_thread_message(
            thread=self,
            message=ChatMessageCreate(
                client_message_id=client_message_id,
                parent=parent_message_id,
                role=role,
                content=content,
                thread=self.chat_thread.thread_id,
                addition_data=addition_data,
            ),
        )

    async def update_message(self,
                             message_id: UUID,
                             content: str,
                             addition_data: Optional[Dict[str, Any]] = None) -> ChatMessageWrapper:
        message = await self.manager.get_message(message_id=message_id)
        return await self.manager.update_thread_message(
            chat_message=message,
            message_data=ChatMessageUpdate(
                content=content,
                addition_data=addition_data,
            ),
        )

    async def append_message(
            self,
            client_message_id: str,
            role: str,
            content: str,
            addition_data: Optional[Dict[str, Any]] = None,
    ):
        try:
            last_message = await self.manager.get_thread_last_message(
                thread_id=self.chat_thread.thread_id
            )
        except DoesNotExist:
            return await self.start_message(
                client_message_id=client_message_id,
                role=role,
                content=content,
                addition_data=addition_data,
            )

        return await last_message.append_message(
            client_message_id=client_message_id,
            role=role,
            content=content,
            addition_data=addition_data,
        )


@dataclasses.dataclass
class ChatManager:
    thread_repo: ChatThreadRepository = None
    message_repo: ChatMessageRepository = None

    async def start_new_thread(
            self, client_thread_id: str, user_id: int
    ) -> ChatThreadWrapper:
        """
        新建一个对话线程

        注意:
            user_id: 创建线程的用户id，此处不负责对用户id的有效性进行检测，应为用户鉴权系统负责
        """
        chat_thread = await self.thread_repo.start_new_thread(
            ChatThreadCreate(client_thread_id=client_thread_id, user_id=user_id)
        )
        return ChatThreadWrapper(manager=self, chat_thread=chat_thread)

    async def list_thread_messages(
            self, thread: ChatThreadWrapper
    ) -> List[ChatMessageWrapper] | None:
        result = await self.message_repo.list_by_thread_id(
            thread_id=thread.chat_thread.thread_id
        )
        if not result:
            return None
        return [
            ChatMessageWrapper(thread_wrapper=thread, chat_message=message)
            for message in result
        ]

    async def get_thread(
            self, thread_id: Optional[UUID] = None, client_thread_id: Optional[str] = None
    ) -> ChatThreadWrapper:
        if client_thread_id:
            thread = await self.thread_repo.get_by_client_id(client_thread_id)
        elif thread_id:
            thread = await self.thread_repo.get_by_id(thread_id)
        else:
            raise ValueError("client_thread_id or thread_id must be provided")

        id_str = f"{thread_id}" if thread_id else f"{client_thread_id}"

        if thread is None:
            raise DoesNotExist(f"thread {id_str} not found")

        return ChatThreadWrapper(manager=self, chat_thread=thread)

    async def get_message(
            self, message_id: Optional[UUID] = None, client_message_id: Optional[str] = None
    ) -> ChatMessageWrapper:
        if client_message_id:
            message = await self.message_repo.get_by_client_id(client_message_id)
        elif message_id:
            message = await self.message_repo.get_by_id(message_id)
        else:
            raise ValueError("client_message_id or message_id must be provided")

        id_str = f"{message_id}" if message_id else f"{client_message_id}"

        if message is None:
            raise DoesNotExist(f"message {id_str} not found")

        thread = await self.get_thread(thread_id=message.thread)
        return ChatMessageWrapper(thread_wrapper=thread, chat_message=message)

    async def new_thread_message(
            self,
            thread: ChatThreadWrapper,
            message: ChatMessageCreate,
    ) -> ChatMessageWrapper:
        return ChatMessageWrapper(
            thread_wrapper=thread, chat_message=await self.message_repo.create(message)
        )

    async def get_thread_last_message(
            self, thread_id: Optional[UUID] = None, client_thread_id: Optional[str] = None
    ) -> ChatMessageWrapper:
        thread = await self.get_thread(
            thread_id=thread_id, client_thread_id=client_thread_id
        )
        last_message = await self.message_repo.get_thread_last_message(
            thread_id=thread.chat_thread.thread_id
        )
        if last_message is None:
            raise DoesNotExist(f"There is no message in thread({thread_id}).")
        return ChatMessageWrapper(thread_wrapper=thread, chat_message=last_message)

    async def update_thread_message(self,
                                    chat_message: ChatMessageWrapper,
                                    message_data: ChatMessageUpdate) -> ChatMessageWrapper:
        return ChatMessageWrapper(
            thread_wrapper=chat_message.thread_wrapper,
            chat_message=await self.message_repo.update(id=chat_message.chat_message.message_id, schema=message_data)
        )
