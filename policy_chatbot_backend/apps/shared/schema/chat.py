import datetime
from typing import Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class ChatThreadCreate(BaseModel):
    client_thread_id: str
    user_id: int


class ChatThreadUpdate(BaseModel):
    update_at: Optional[datetime.datetime]


class ChatMessageCreate(BaseModel):
    client_message_id: str
    thread: UUID  # 对应thread_id
    role: str
    content: str
    parent: Optional[UUID] = None  # 对应message_id
    addition_data: Dict[str, Any] = Field(default_factory=dict)


class ChatMessageUpdate(BaseModel):
    parent_id: Optional[UUID] = None  # 理论上用不到
    content: Optional[str] = None
    addition_data: Optional[Dict[str, Any]] = None
