from typing import List, NamedTuple, <PERSON><PERSON>, Optional


class ChunkText(NamedTuple):
    content: str
    source: str
    version: str
    doc_id: Optional[int] = None

    def __repr__(self):
        return self._asdict().__str__()

    def items(self):
        return self._asdict().items()


class ChunkWithReference(NamedTuple):
    content: str
    reference: str
    reference_id: str

    def __repr__(self):
        return self._asdict().__str__()

    def items(self):
        return self._asdict().items()


class ChunkReference(NamedTuple):
    reference: str
    reference_id: str

    def __repr__(self):
        return self._asdict().__str__()

    def items(self):
        return self._asdict().items()


def convert_chunk_references(
        chunks: List[ChunkText],
) -> Tuple[List[ChunkWithReference], List[ChunkReference]]:
    references = {}
    ref_id_count = 1
    result = []
    for chunk in chunks:
        title = chunk.source
        if chunk.version and chunk.version.strip():
            title = f"{title} （{chunk.version}）"

        reference = title

        if reference not in references:
            references[reference] = ref_id_count
            ref_id_count += 1
        ref_id = references[reference]
        result.append(
            ChunkWithReference(
                content=chunk.content,
                reference=reference,
                reference_id=ref_id,
            )
        )
    return result, [
        ChunkReference(reference=ref, reference_id=ref_id)
        for ref, ref_id in references.items()
    ]
