import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


# Document
class DocumentCreate(BaseModel):
    source: str
    version: str
    available: bool = False
    permission_role: Optional[int] = Field(
        None,
        description="文档的权限组设置，因为权限组可能存在变化，允许留null作为无权限要求",
    )
    mark_as_qa: bool = Field(False, description="是否设置为QA文档")
    qa_description: str = Field(
        "", description="若设置为QA文档，则需填写其对应的QA问题集介绍"
    )


class CreateDocumentRequest(DocumentCreate):
    pass


class DocumentUpdate(BaseModel):
    source: Optional[str] = None
    version: Optional[str] = None
    available: Optional[bool] = None
    permission_role: Optional[int] = None
    updated_at: Optional[datetime.datetime] = datetime.datetime.now()
    mark_as_qa: Optional[bool] = Field(
        None, description="是否设置为QA文档，为空则为不操作，否则进行同步修改"
    )
    qa_description: Optional[str] = Field(
        None, description="若设置为QA文档，则需填写其对应的QA问题集介绍，可以为null"
    )


class UpdateDocumentRequest(DocumentUpdate):
    pass


class DocumentResponse(BaseModel):
    document_id: int
    source: str
    version: str
    available: bool = True
    permission_role: Optional[int] = None  # TODO 返回详细的信息
    created_at: datetime.datetime
    updated_at: datetime.datetime

    mark_as_qa: bool = False
    qa_description: str | None = None

    class Config:
        from_attributes = True


class DocumentsListResponse(BaseModel):
    total: int
    documents: List[DocumentResponse]


# Chunk
class ChunkCreate(BaseModel):
    content: str
    embedding: List[float]
    document: Optional[int] = None


class ChunkUpdate(BaseModel):
    content: Optional[str] = None
    embedding: Optional[List[float]] = None
    document: Optional[int] = None


class ChunkResponse(BaseModel):
    chunk_id: int
    content: str
    created_at: datetime.datetime
    updated_at: datetime.datetime
    document: int

    class Config:
        from_attributes = True


class ChunksListResponse(BaseModel):
    total: int
    chunks: List[ChunkResponse]


class DocumentsSearchResult(BaseModel):
    total: int
