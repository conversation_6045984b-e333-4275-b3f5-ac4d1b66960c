from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field


class UserAuthProvider(Enum):
    LOCAL = "local"
    CAS = "cas"
    ALL = "all"


class UserCreate(BaseModel):
    username: str
    nickname: Optional[str] = None
    hashed_password: Optional[str] = Field(None, description="密码，统一认证时可为None")

    cas_uuid: Optional[str] = Field(None, description="cas uuid，如果非统一认证登录则返回None")
    auth_providerL: str = Field(UserAuthProvider.LOCAL.value, description="认证方式")  # local、cas、all

    role: Optional[int] = None


class UserUpdate(BaseModel):
    nickname: Optional[str] = None
    hashed_password: Optional[str] = None

    cas_uuid: Optional[str] = None
    auth_providerL: Optional[str] = None
    role: Optional[int] = None


class UserRoleCreate(BaseModel):
    role_code: str
    permissions: Optional[list[str]]


class UserInfoCreate(BaseModel):
    user: int
    department: Optional[str] = None
    major: Optional[str] = None
    admission_year: Optional[int] = None
    birth_year: Optional[int] = None


class UserInfoUpdate(BaseModel):
    department: Optional[str] = None
    major: Optional[str] = None
    admission_year: Optional[int] = None
    birth_year: Optional[int] = None


class ActionLogCreate(BaseModel):
    user: int
    action_type: str
    ip_address: str
    details: Optional[str] = None
    user_agent: Optional[str] = None
