from enum import Enum
from typing import AsyncGenerator, List, Tuple

from apps.shared.repository.policy import (
    PolicyChunkRepository, DocumentFilterParams,
)
from apps.shared.schema.document_chunk import ChunkText
from core.agents.query_optimize import decompose_document_query, stepback_document_query
from core.logger import get_logger
from core.rag.chunks_rerank import rerank_client
from core.rag.embedding import embedding_client
from settings import settings

logger = get_logger(__name__)


class RetrieveStepResult(Enum):
    OPTIMIZE_QUERY = "optimize_query"
    RETRIEVE_DOCUMENT = "retrieve_document"
    FINISHED = "finished"

    ERROR = "error"


class DocumentRetriever:
    top_K = settings.RAG_RETRIEVE_TOP_K
    retrieve_scale_factor = settings.RAG_RETRIEVE_SCALE_FACTOR

    policy_chunk_repo: PolicyChunkRepository = None

    def __init__(
            self,
            policy_chunk_repo: PolicyChunkRepository,
    ):
        self.policy_chunk_repo = policy_chunk_repo

    async def _embedding_query(self, query: str) -> List:
        return await embedding_client.embed_query([query])

    # TODO 加入文件权限过滤
    async def retrieve_document(
            self,
            query: str,
            top_K: int = top_K,
            filter_params: DocumentFilterParams = None,
            be_qa: bool = False,
    ) -> List[ChunkText]:
        """普通的政策文件检索，不针对query做出任何优化

        Args:
            top_K: 检索出最近相邻的 top_K 个文档块
            query (str): 查询语句
            filter_params : 文档过滤参数
            be_qa (bool): true时只检索qa文档，false只检索普通文档

        Returns:
            List[ChunkText]
        """
        query_embedding = await self._embedding_query(query)

        if be_qa:
            chunks = await self.policy_chunk_repo.retrieve_qa_document(
                query_embedding, top_K=top_K, filter_params=filter_params
            )
        else:
            chunks = await self.policy_chunk_repo.retrieve_embedding(
                query_embedding, top_K=top_K, include_qa=False, filter_params=filter_params
            )

        return [
            ChunkText(
                content=chunk.content,
                source=chunk.document.source,
                version=chunk.document.version,
                doc_id=chunk.chunk_id
            )
            for chunk in chunks
        ]

    async def retrieve_all_chunks(self, doc_id: int) -> List[ChunkText]:
        chunks = await self.policy_chunk_repo.fetch_all(
            self.policy_chunk_repo.list_with_document_id(document_id=doc_id, limit=None, skip=None))
        return [
            ChunkText(
                content=chunk.content,
                source=chunk.document.source,
                version=chunk.document.version,
                doc_id=chunk.chunk_id
            )
            for chunk in chunks
        ]

    async def retrieve_chunks_in_document(self, query: str, doc_id: int):
        chunks = await self.policy_chunk_repo.retrieve_embedding_in_document(
            query_embedding=await self._embedding_query(str(query)),
            top_K=self.top_K,
            document_id=doc_id
        )
        return [
            ChunkText(
                content=chunk.content,
                source=chunk.document.source,
                version=chunk.document.version,
                doc_id=chunk.chunk_id
            )
            for chunk in chunks
        ]

    async def rag_retrieve_document(
            self,
            query: str,
            context: str,
            filter_params: DocumentFilterParams = None,
    ) -> AsyncGenerator[Tuple[RetrieveStepResult, str | List[ChunkText]], None]:
        """RAG的高级文档检索，针对query自动优化

        *考虑到用户可能只回复一个单词，让ChatBot通过上下文推测完整的问题，所以将stepback和rewrite合并*

        ## 查询流程
        1. query优化
            a. stepback 根据上下文，将问题具体化
            b. decompose 子查询分解
        2. 子查询联合查询
        3. 子查询整合重排 (Rerank)

        Args:
            query (str): 查询语句
            context (str): 问题的上下文
            filter_params : 文档过滤参数

        Returns:
            AsyncGenerator[(RetrieveStepResult, str | List[ChunkText]), None] :
                    逐步返回每步骤的结果，若为 RetrieveStep.FINISHED，则第二参数为 List[ChunkText] 查询结果，否则为当前步骤的状态反馈
        """

        optimized_query = await stepback_document_query(query, context)
        if not optimized_query:
            yield RetrieveStepResult.ERROR, "优化问题失败"
            return
        yield RetrieveStepResult.OPTIMIZE_QUERY, optimized_query

        subqueries = await decompose_document_query(optimized_query)
        if not subqueries:
            yield RetrieveStepResult.ERROR, "多角度思考问题失败"
            return

        subquery_chunks = []
        for subquery in subqueries:
            yield RetrieveStepResult.RETRIEVE_DOCUMENT, subquery
            subquery_chunks.extend(
                await self.retrieve_document(
                    subquery,
                    top_K=round(self.top_K * self.retrieve_scale_factor),
                    be_qa=True,
                    filter_params=filter_params,
                )
            )
            subquery_chunks.extend(
                await self.retrieve_document(
                    subquery,
                    top_K=round(self.top_K * self.retrieve_scale_factor),
                    be_qa=False,
                    filter_params=filter_params,
                )
            )

        if len(subquery_chunks) == 0:
            logger.warning(f"Query({query}) related to no chunks.")
            yield RetrieveStepResult.FINISHED, []
            return

        if len(subquery_chunks) <= self.top_K:
            yield RetrieveStepResult.FINISHED, subquery_chunks
            return

        # 文档回溯
        # 如果同一个chunk出现的次数大于2则认为这个文档全文重要，检索出全部的文档信息
        # 如果文档信息过长，则检索出文档的top_K个chunk
        document_map = {}
        for chunk in subquery_chunks:
            if chunk.doc_id not in document_map:
                chunk_list = [chunk]
                document_map[chunk.doc_id] = chunk_list
                continue
            document_map[chunk.doc_id].append(chunk)

        for doc_id, chunk_list in document_map.items():
            if len(chunk_list) <= settings.RAG_RETRIEVE_ALL_THRESHOLD_CHUNK_COUNT:
                continue
            # 进行回溯
            logger.debug(f"Document({doc_id}) has {len(chunk_list)} chunks, try to retrieve all.")
            new_chunk_list = []
            all_chunks = await self.retrieve_all_chunks(doc_id)
            new_chunk_list.extend(all_chunks)
            document_map[doc_id] = new_chunk_list

        to_rerank_chunk_text = []  # 会再进行rerank
        final_chunk_text = []  # 排在to_rerank_chunk_text前面，不参与rerank，以避免超出reranker的最大上下文
        for doc_id, chunk_list in document_map.items():
            if len(chunk_list) == 0:
                continue

            def merge_chunks(chunks) -> ChunkText:
                content = ""
                for chunk in chunks:
                    content += f"\n{chunk.content}"
                return ChunkText(
                    content=content,
                    source=chunk_list[0].source,
                    version=chunk_list[0].version,
                )

            chunk_text = merge_chunks(chunk_list)
            # 如果超出最大能接受的长度，则重新检索文档内top_K
            if len(chunk_text.content) >= settings.RAG_RETRIEVE_ALL_MAX_WORDS:
                logger.debug(f"Document({doc_id}) has {len(chunk_list)} chunks, try to retrieve in document.")
                new_chunk_list = await self.retrieve_chunks_in_document(query, doc_id)
                chunk_text = merge_chunks(new_chunk_list)

            # 分类是否rerank
            if len(chunk_list) >= settings.RAG_RETRIEVE_ALL_THRESHOLD_CHUNK_COUNT:
                final_chunk_text.append(chunk_text)
            else:
                to_rerank_chunk_text.append(chunk_text)

        to_reranked_chunks = [
            f"[{chunk.source}-{chunk.version}]{chunk.content}"
            for chunk in subquery_chunks
        ]

        rank_indexes = await rerank_client.rerank(
            str(query),
            to_reranked_chunks,
            top_n=self.top_K,
            return_documents=False,
        )

        if not rank_indexes or len(rank_indexes) == 0:
            logger.error(f"Rerank({query}) failed.( chunks = {to_reranked_chunks})")
            yield RetrieveStepResult.FINISHED, subquery_chunks[: self.top_K]

        result = [subquery_chunks[i] for i in rank_indexes]
        result.extend(final_chunk_text)
        # TODO 基于文档的tags与filter_params的tags再rerank

        yield RetrieveStepResult.FINISHED, result
