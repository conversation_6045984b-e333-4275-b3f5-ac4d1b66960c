from functools import lru_cache

from fastapi import Depends

from apps.chat.chat_manager import Chat<PERSON>anager
from apps.shared.document_retriever import DocumentRetriever
from apps.shared.repository.chat import ChatThreadRepository, ChatMessageRepository
from apps.shared.repository.policy import (
    PolicyChunkRepository,
)
from core.depends import get_repository_dep


@lru_cache
def get_document_retriever_dep():
    def _get_retriever(
            chunk_repo=get_repository_dep(PolicyChunkRepository),
    ):
        return DocumentRetriever(
            policy_chunk_repo=chunk_repo,
        )

    return Depends(_get_retriever)


@lru_cache
def get_chat_manager_dep():
    def _get_manager(
            thread_repo=get_repository_dep(ChatThreadRepository),
            message_repo=get_repository_dep(ChatMessageRepository)
    ):
        return ChatManager(
            thread_repo=thread_repo,
            message_repo=message_repo
        )

    return Depends(_get_manager)
