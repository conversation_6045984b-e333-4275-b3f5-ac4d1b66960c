import datetime
import uuid

from peewee import (
    Char<PERSON><PERSON>,
    DateTimeField,
    TextField, ForeignKeyField, AutoField, UUIDField,
)

from apps.shared.model.user import User
from core.models import BaseModel


class ChatThread(BaseModel):
    thread_id = UUIDField(primary_key=True, default=uuid.uuid4)
    client_thread_id = CharField(max_length=255, null=False)  # 客户端记录的thread_id，于后端数据库可重复

    user_id = ForeignKeyField(User, backref="chat_history", null=False)

    created_at = DateTimeField(default=datetime.datetime.now)
    update_at = DateTimeField(default=datetime.datetime.now)

    class Meta:
        table_name = "chat_threads"
        indexes = ((("thread_id",), True),
                   (("client_thread_id", "user_id"), True),)


class ChatMessage(BaseModel):
    message_id = UUIDField(primary_key=True, default=uuid.uuid4)
    client_message_id = Char<PERSON>ield(max_length=255, null=False)  # 客户端记录的message_id，于后端数据库可重复

    thread = ForeignKeyField(ChatThread, backref="messages", null=False)

    created_at = DateTimeField(default=datetime.datetime.now)
    parent = ForeignKeyField("self", backref="children", null=True)

    role = CharField(max_length=25, null=False)  # user, assistant, tool
    content = TextField(null=False)  # JSON data: List[Dict]
    addition_data = TextField(null=False, default="{}")  # 存储前端需要的 Message 附加信息，metadata, attachments, ...

    class Meta:
        table_name = "chat_messages"
        indexes = ((("message_id",), True),
                   (("client_message_id", "thread"), True),)


class ChatFeedback(BaseModel):
    feedback_id = AutoField()
    message_id = ForeignKeyField(ChatMessage, backref="feedback", null=False)
    feedback = TextField(null=False)
    created_at = DateTimeField(default=datetime.datetime.now)

    class Meta:
        table_name = "chat_feedback"
