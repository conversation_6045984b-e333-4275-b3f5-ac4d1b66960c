import datetime

from peewee import (
    AutoField,
    CharField,
    DateTimeField,
    ForeignKeyField,
    TextField,
    BooleanField,
    IntegerField,
)
from pgvector.peewee import VectorField

from apps.shared.model.user import UserRole
from core.models import BaseModel
from settings import settings


class PolicyTag(BaseModel):
    tag_id = AutoField()
    tag_name = CharField(max_length=255, null=False, unique=True)

    class Meta:
        table_name = "policy_tags"
        indexes = ((("tag_id",), True),)


class PolicyGrade(BaseModel):
    grade_id = AutoField()
    start_year = IntegerField()
    end_year = IntegerField()

    class Meta:
        table_name = "policy_grades"
        indexes = ((("start_year", "end_year"), True),)


# 政策文档表
class PolicyDocument(BaseModel):
    document_id = AutoField()
    source = CharField(max_length=8096, null=False)
    version = CharField(max_length=200, null=False)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    available = BooleanField(default=False)

    permission_role = ForeignKeyField(UserRole, backref="policy_documents", null=True)

    mark_as_qa = BooleanField(default=False)
    qa_description = TextField(null=True)

    class Meta:
        table_name = "policy_documents"
        indexes = ((("source", "version"), True),)


class PolicyDocumentGrade(BaseModel):
    document = ForeignKeyField(
        PolicyDocument, backref="policy_document_grades", null=True
    )
    grade = ForeignKeyField(PolicyGrade, backref="policy_document_grades")

    class Meta:
        table_name = "policy_document_grades"
        indexes = ((("document", "grade"), True),)


class PolicyDocumentTag(BaseModel):
    tag = ForeignKeyField(PolicyTag, backref="policy_document_tags")
    document = ForeignKeyField(PolicyDocument, backref="policy_document_tags")

    class Meta:
        table_name = "policy_document_tags"
        indexes = ((("tag", "document"), True),)


# 政策切块表
class PolicyChunk(BaseModel):
    chunk_id = AutoField()
    content = TextField(null=False)
    embedding = VectorField(dimensions=settings.EMBEDDING_DIMENSIONS)
    document = ForeignKeyField(PolicyDocument, backref="policy_chunks", null=True)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)

    class Meta:
        table_name = "policy_chunks"
        indexes = ((("chunk_id",), True),)
