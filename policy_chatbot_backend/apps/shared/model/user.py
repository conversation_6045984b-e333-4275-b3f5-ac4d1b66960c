from datetime import datetime

from peewee import AutoField, CharField, ForeignKeyField, DateTimeField, IntegerField, TextField

from core.models import BaseModel


class UserRole(BaseModel):
    role_id = AutoField()
    role_code = CharField(unique=True, max_length=255)  # 如admin > manager > teacher > student
    permissions = TextField(null=True)  # JSON，admin不需要遵守，其余的会根据permission管理功能组的使用

    class Meta:
        table_name = "user_roles"
        indexes = ((("role_id",), True),)


# TODO 上线前，需要修改线上数据库的表结构
class User(BaseModel):
    user_id = AutoField(primary_key=True)
    username = Char<PERSON><PERSON>(max_length=255, unique=True)  # 除去老师的账号，一站式的需要通过学号作为username
    nickname = CharField(max_length=50, null=True)  # 昵称，现在认为是真名
    hashed_password = CharField(max_length=255, null=True)  # 仅为本系统注册用户使用，

    cas_uuid = CharField(max_length=255, null=True)  # 仅用于CAS用户
    auth_provider = CharField(max_length=10, null=False, default="local",
                              choices=[("local", "本地"), ("cas", "统一认证"), ("all", "所有")])  # local、cas、all
    created_at = DateTimeField(default=datetime.now)

    role = ForeignKeyField(UserRole, backref="users", null=False)  # 为None则为最低权限

    class Meta:
        table_name = "users"
        indexes = ((("user_id",), True),)


class UserInfo(BaseModel):
    user = ForeignKeyField(User, backref="user_info", null=False, primary_key=True)
    department = CharField(max_length=255, null=True)
    major = CharField(max_length=255, null=True)
    admission_year = IntegerField(null=True)
    birth_year = IntegerField(null=True)

    class Meta:
        table_name = "user_info"


# TODO 创建对应的存储逻辑
class UserActionLog(BaseModel):
    id = AutoField()
    user = ForeignKeyField(User, backref='action_logs')  # 关联操作用户
    action_type = CharField(max_length=128)  # 操作类型如login/logout/update
    details = TextField(null=True)  # 操作详细信息
    ip_address = CharField(max_length=156)  # 记录客户端IP
    user_agent = TextField(null=True)  # 用户客户端信息
    created_at = DateTimeField(default=datetime.now)

    class Meta:
        table_name = "user_action_log"
