from typing import List, Optional

from pydantic import BaseModel

from apps.shared.model.policy import (
    PolicyChunk,
    PolicyDocument,
    PolicyDocumentTag,
    PolicyTag,
    PolicyDocumentGrade,
)
from apps.shared.schema.policy import (
    ChunkCreate,
    ChunkUpdate,
    DocumentCreate,
    DocumentUpdate,
)
from core.rag.chunker import chunk_document
from core.rag.embedding import embedding_client
from core.repository import BaseRepository
from peewee import ModelSelect, NodeList, SQL, DoesNotExist
from apps.auth.auth import USER_ROLE_PERMISSION_MAP, parse_user_role_permission_id
import logging

logger = logging.getLogger(__name__)


class DocumentFilterParams(BaseModel):
    permission_role_code: Optional[str]
    recommended_tags: Optional[List[str]] = (
        None  # 优先显示的tags，在rerank时参与比较，每个tag权重为1，允许叠加
    )
    needed_tags: Optional[List[str]] = None  # 必须包含的tags


class PolicyDocumentRepository(
    BaseRepository[PolicyDocument, DocumentCreate, DocumentUpdate]
):
    def __init__(self):
        super().__init__(PolicyDocument)

    async def mark_as_qa(self, document_id: int, description: Optional[str] = None):
        document = await self.get_or_error(document_id)
        if document is None:
            raise DoesNotExist(f"Document({document_id}) not found")
        await self.update(
            document_id, {"mark_as_qa": True, "qa_description": description}
        )

    async def delete_document(self, document_id: int) -> bool:
        """
        删除文档及其所有关联数据
        :param document_id: 文档ID
        :return: 是否删除成功
        """
        try:
            # 1. 获取文档实例
            document = await self.get_by_id(document_id)
            if not document:
                return False

            # 2. 删除所有关联的切块
            await PolicyChunkRepository().delete_by_document_id(document_id)

            # 3. 删除所有关联的标签
            await PolicyDocumentTagRepository().delete_by_document_id(document_id)

            # 4. 删除所有关联的年级
            await PolicyDocumentGradeRepository().delete_by_document_id(document_id)

            # 5. 删除文档本身
            await self._delete_instance(document)

            return True
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {str(e)}")
            return False

    async def delete_by_id(self, id: int) -> bool:
        """
        删除文档（内部方法）
        :param id: 文档ID
        :return: 是否删除成功
        """
        try:
            document = await self.get_by_id(id)
            if document:
                await self._delete_instance(document)
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete document {id}: {str(e)}")
            return False


class PolicyChunkRepository(BaseRepository[PolicyChunk, ChunkCreate, ChunkUpdate]):
    def __init__(self):
        super().__init__(PolicyChunk)

    def list_with_document_id(
        self,
        document_id: int,
        skip: Optional[int] = 0,
        limit: Optional[int] = 10,
    ) -> ModelSelect:
        return (
            (self.list(skip=skip, limit=limit))
            .where(PolicyChunk.document == document_id)
            .order_by(PolicyChunk.chunk_id)
        )

    async def count_with_document_id(self, document_id: int) -> int:
        return await self.count_query(self.list_all(document_id=document_id))

    def _gen_retrieve_embedding_query(
        self,
        query_embedding: List,
        top_K: int,
        filter_params: DocumentFilterParams = None,
    ) -> ModelSelect:
        distance_expr = (
            PolicyChunk.embedding.cosine_distance(query_embedding) * 0.8
            + PolicyChunk.embedding.l2_distance(query_embedding) * 0.2
        )

        query = (
            self.model.select(self.model, PolicyDocument)
            .join(
                PolicyDocument,
                on=(PolicyChunk.document == PolicyDocument.document_id),
            )
            .where(PolicyDocument.available)
        )

        if filter_params and filter_params.permission_role_code is not None:
            # 将当前用户的角色转换为权限等级
            user_permission_priority = parse_user_role_permission_id(
                filter_params.permission_role_code
            )

            # 获取所有权限等级小于等于当前用户的角色代码，即比自己优先级高的角色代码
            allowed_roles = [
                role_code
                for role_code, pid in USER_ROLE_PERMISSION_MAP.items()
                if pid <= user_permission_priority
            ]

            query = query.where(
                (PolicyDocument.permission_role.is_null())  # 允许无权限要求的文档
                | (
                    PolicyDocument.permission_role.role_code.in_(allowed_roles)
                )  # 用户权限等级 >= 文档要求
            )

        if filter_params and filter_params.needed_tags is not None:
            # 需要根据PolicyDocumentTag判断，要求PolicyDocument的tag必须包含这些tag
            for tag_name in filter_params.needed_tags:
                subquery = (
                    PolicyDocumentTag.select()
                    .join(PolicyTag)
                    .where(
                        (PolicyDocumentTag.document == PolicyDocument.document_id)
                        & (PolicyTag.tag_name == tag_name)
                    )
                )
                query = query.where(NodeList((SQL("EXISTS"), subquery)))

        query = query.order_by(distance_expr).limit(top_K)
        return query

    async def retrieve_embedding_in_document(
        self, query_embedding: List, top_K: int, document_id: int
    ) -> List[PolicyChunk]:
        return await self.fetch_all(
            self._gen_retrieve_embedding_query(query_embedding, top_K).where(
                PolicyChunk.document == document_id
            )
        )

    async def retrieve_embedding(
        self,
        query_embedding: List,
        top_K: int,
        filter_params: DocumentFilterParams = None,
        include_qa: bool = False,
    ) -> List[PolicyChunk]:
        query = self._gen_retrieve_embedding_query(
            query_embedding, top_K, filter_params
        )
        if not include_qa:
            query = query.where(PolicyDocument.mark_as_qa == False)

        return await self.fetch_all(query)

    async def retrieve_qa_document(
        self,
        query_embedding: List,
        top_K: int,
        filter_params: DocumentFilterParams = None,
    ) -> List[PolicyChunk]:
        query = self._gen_retrieve_embedding_query(
            query_embedding, top_K, filter_params
        )
        query = query.where(PolicyDocument.mark_as_qa == True)
        return await self.fetch_all(query)

    async def import_from_markdown(
        self, document_id: int, content: str
    ) -> List[PolicyChunk]:
        chunks = await chunk_document(content)
        chunks_create = [
            ChunkCreate(
                document=document_id,
                content=chunk,
                embedding=await embedding_client.embed_passage([chunk]),
            )
            for chunk in chunks
        ]

        result = []

        for chunk_create in chunks_create:
            chunk = await self.create(chunk_create)
            result.append(
                PolicyChunk(
                    chunk_id=chunk.chunk_id,
                    content=chunk.content,
                    document=chunk.document.document_id,
                    created_at=chunk.created_at,
                    updated_at=chunk.updated_at,
                )
            )

        return result

    async def delete_by_document_id(self, document_id: int) -> bool:
        """
        删除文档的所有切块
        :param document_id: 文档ID
        :return: 是否删除成功
        """
        try:
            query = self.list_all(document=document_id)
            chunks = await self.fetch_all(query)
            for chunk in chunks:
                await self._delete_instance(chunk)
            return True
        except Exception as e:
            logger.error(
                f"Failed to delete chunks for document {document_id}: {str(e)}"
            )
            return False


class PolicyDocumentTagRepository(BaseRepository[PolicyDocumentTag, None, None]):
    def __init__(self):
        super().__init__(PolicyDocumentTag)

    async def delete_by_document_id(self, document_id: int) -> bool:
        """
        删除文档的所有标签关联
        :param document_id: 文档ID
        :return: 是否删除成功
        """
        try:
            query = self.list_all(document=document_id)
            tags = await self.fetch_all(query)
            for tag in tags:
                await self._delete_instance(tag)
            return True
        except Exception as e:
            logger.error(f"Failed to delete tags for document {document_id}: {str(e)}")
            return False


class PolicyDocumentGradeRepository(BaseRepository[PolicyDocumentGrade, None, None]):
    def __init__(self):
        super().__init__(PolicyDocumentGrade)

    async def delete_by_document_id(self, document_id: int) -> bool:
        """
        删除文档的所有年级关联
        :param document_id: 文档ID
        :return: 是否删除成功
        """
        try:
            query = self.list_all(document=document_id)
            grades = await self.fetch_all(query)
            for grade in grades:
                await self._delete_instance(grade)
            return True
        except Exception as e:
            logger.error(
                f"Failed to delete grades for document {document_id}: {str(e)}"
            )
            return False
