from typing import List
from uuid import UUID

from peewee import Integ<PERSON><PERSON><PERSON>r, DoesNotExist

from apps.shared.model.chat import ChatThread, ChatMessage
from apps.shared.schema.chat import (
    ChatThreadCreate,
    ChatThreadUpdate,
    ChatMessageCreate,
    ChatMessageUpdate,
)
from core.async_utils import to_async_io
from core.repository import BaseRepository


class ChatThreadRepository(
    BaseRepository[ChatThread, ChatThreadCreate, ChatThreadUpdate]
):
    def __init__(self):
        super().__init__(model=ChatThread)

    async def get_by_client_id(self, client_thread_id: str) -> ChatThread | None:
        return await self.get_or_none(client_thread_id=client_thread_id)

    async def get_by_id(self, thread_id: UUID) -> ChatThread | None:
        return await self.get_or_none(thread_id=thread_id)

    async def start_new_thread(self, params: ChatThreadCreate) -> ChatThread:
        if await self.get_by_client_id(params.client_thread_id):
            raise IntegrityError(f"thread({params.client_thread_id}) already exists")
        else:
            return await self.create(params)


class ChatMessageRepository(
    BaseRepository[ChatMessage, ChatMessageCreate, ChatMessageUpdate]
):
    def __init__(self):
        super().__init__(model=ChatMessage)

    async def get_by_id(self, message_id: UUID) -> ChatMessage | None:
        return await self.get_or_none(message_id=message_id)

    async def get_by_client_id(self, client_message_id: str) -> ChatMessage | None:
        return await self.get_or_none(client_message_id=client_message_id)

    async def list_by_thread_id(self, thread_id: UUID) -> List[ChatMessage] | None:
        try:
            return await self.fetch_all(self.list_all(thread=thread_id))
        except DoesNotExist:
            return None

    async def get_thread_last_message(self, thread_id: UUID) -> ChatMessage | None:
        return await self.fetch_one_or_none(
            (self.list_all(thread=thread_id)).order_by(ChatMessage.created_at.desc())
        )
