from datetime import datetime
from typing import Optional

from apps.shared.model.user import User, UserRole, UserActionLog, UserInfo
from apps.shared.schema.user import UserCreate, UserUpdate, UserInfoUpdate, UserInfoCreate, ActionLogCreate, \
    UserRoleCreate
from core.repository import BaseRepository


class UserRoleRepository(BaseRepository[UserRole, UserRoleCreate, ...]):
    def __init__(self):
        super().__init__(UserRole)

    async def get_role_by_code(self, role_code: str) -> UserRole | None:
        """根据角色代码获取角色"""
        return await self.get_or_none(UserRole.role_code == role_code)


class UserInfoRepository(BaseRepository[UserInfo, UserInfoCreate, UserInfoUpdate]):  # 替换为实际Schema类型
    def __init__(self):
        super().__init__(UserInfo)


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    def __init__(self):
        super().__init__(User)

    async def get_user_by_username(self, username: str) -> User | None:
        return await self.get_or_none(username=username)


class UserActionLogRepository(BaseRepository[UserActionLog, ActionLogCreate, ...]):
    def __init__(self):
        super().__init__(UserActionLog)

    async def get_user_activity(
            self,
            user_id: int,
            start_time: Optional[datetime] = None,
            end_time: Optional[datetime] = None
    ) -> list[UserActionLog]:
        """获取用户操作记录（支持时间范围可选）"""
        base_condition = (UserActionLog.user == user_id)

        # 动态构建时间条件
        time_conditions = []
        if start_time is not None:
            time_conditions.append(UserActionLog.created_at >= start_time)
        if end_time is not None:
            time_conditions.append(UserActionLog.created_at <= end_time)

        # 组合所有条件
        query = self.model.select().where(
            base_condition & (*time_conditions, True)
        ).order_by(UserActionLog.created_at.desc())

        return await self.fetch_all(query)
