import dataclasses
import logging
from typing import List

from peewee import IntegrityError

from apps.shared.document_retriever import DocumentRetriever
from apps.shared.repository.policy import (
    PolicyChunkRepository,
    PolicyDocumentRepository,
)
from apps.shared.schema.policy import DocumentC<PERSON>, ChunkCreate
from core.rag.chunker import chunk_document
from core.rag.embedding import embedding_client
from core.logger import get_logger

logger = get_logger(__name__)


@dataclasses.dataclass
class DocumentStorer:
    document_repo: PolicyDocumentRepository = None
    chunks_repo: PolicyChunkRepository = None

    async def split_document(self, text: str) -> List[str]:
        return await chunk_document(text)

    async def store_document(self, text: str, source: str, version: str):
        document = await self.document_repo.get_or_none(source=source, version=version)

        if document is None:
            document = await self.document_repo.create(
                DocumentCreate(source=source, version=version, available=False)
            )

        chunks = await self.split_document(text)

        if not chunks or len(chunks) == 0:
            logger.error(f"Chunk Document({source}-{version}) failed. ( `chunks` is None or empty. )")
            raise Exception(f"{source}-{version} 文档切块失败")

        for chunk in chunks:
            try:
                embedding = await embedding_client.embed_passage([chunk])
                if not embedding:
                    logger.error(f"store_document: embedding error => {chunk}")
                    continue
                await self.chunks_repo.create(
                    ChunkCreate(content=chunk, embedding=embedding, document=document)
                )

            except IntegrityError as e:
                logging.warning(f"Create PolicyChunk with same primary key: {e} \n ({source}, {version})")
                raise e
            except Exception as e:
                logging.error(f"Failed to create PolicyChunk: {e}")
                raise e


class DocumentManager(DocumentRetriever, DocumentStorer):
    pass
