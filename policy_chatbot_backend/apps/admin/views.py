from io import BytesIO
from typing import Optional

from docling_core.types.io import DocumentStream
from fastapi import APIRouter, Query, HTTPException, UploadFile
from fastapi.params import Depends, Body, File, Path
from peewee import DoesNotExist, IntegrityError
from pydantic import Field, BaseModel

from apps.auth.auth import UserRoleCode, RoleScopeChecker, USER_ROLE_PERMISSION_MAP
from apps.shared.model.policy import PolicyDocument
from apps.shared.repository.policy import (
    PolicyChunkRepository,
    PolicyDocumentRepository,
)
from apps.shared.schema.policy import (
    ChunksListResponse,
    CreateDocumentRequest,
    DocumentsListResponse,
    DocumentCreate,
    ChunkResponse,
    DocumentResponse,
    DocumentUpdate,
    ChunkCreate,
    ChunkUpdate,
    UpdateDocumentRequest,
)
from core.depends import get_repository_dep
from core.document_parser import convert_file_to_markdown
from core.rag.embedding import embedding_client

admin_router = APIRouter(
    tags=["admin"],
    dependencies=[Depends(RoleScopeChecker(UserRoleCode.MANAGER.value))],
)

### 文档管理 API
"""
# 创建文档时，客户端有两大选项：
1. 从PDF、WORD文件文档导入（需要标注内容是一个文档，还是多个文档的聚合；是QA问题集，还是普通政策文档），自动创建新的文档，并导入
2. 创建空的文档；QA文档可以通过标注某个文档设置（即，QA只是有特殊属性标注的政策文档）

# 文档的权限
在文档创建后设置，导入的也一样，之后才能设置权限
"""


class ParseDocumentResponse(BaseModel):
    content: str


@admin_router.post("/documents/parse", response_model=ParseDocumentResponse)
async def parse_document_from_file(
        file: UploadFile = File(..., description="上传的PDF、docx文档文件"),
):
    content = await file.read()
    stream = BytesIO(content)
    document_stream = DocumentStream(stream=stream, name=file.filename)
    return ParseDocumentResponse(
        content=await convert_file_to_markdown(document_stream)
    )


@admin_router.post("/documents", response_model=DocumentResponse)
async def create_empty_document(
        params: CreateDocumentRequest = Body(...),
        document_repo=get_repository_dep(PolicyDocumentRepository),
):
    try:
        # 验证permission_role是否合法
        if params.permission_role is not None:
            if params.permission_role not in USER_ROLE_PERMISSION_MAP.values():
                raise HTTPException(
                    status_code=400, detail="Invalid permission_role value"
                )

        # 检查文档是否已存在
        existing_document = await document_repo.get_or_none(source=params.source, version=params.version)
        if existing_document is not None:
            raise HTTPException(
                status_code=409, detail="(Source,Version) already exists."
            )

        document: PolicyDocument = await document_repo.create(params)
        return DocumentResponse(
            document_id=document.document_id,
            source=document.source,
            version=document.version,
            available=document.available,
            permission_role=document.permission_role.role_id
            if document.permission_role
            else None,
            created_at=document.created_at,
            updated_at=document.updated_at,
            mark_as_qa=document.mark_as_qa,
            qa_description=document.qa_description,
        )
    except IntegrityError as e:
        if "source" in str(e).lower() and "version" in str(e).lower():
            raise HTTPException(
                status_code=409, detail="(Source,Version) already exists."
            )
        raise HTTPException(status_code=400, detail=f"Invalid parameters: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Create document error: {e}")


@admin_router.post("/documents/{document_id}/import", response_model=ChunksListResponse)
async def import_document_from_markdown(
        content: str = Body(..., description="上传的markdown文档文件", embed=True),
        document_id: int = Path(..., description="要导入的文档ID"),
        chunk_repo=get_repository_dep(PolicyChunkRepository),
        document_repo=get_repository_dep(PolicyDocumentRepository),
):
    try:
        document = await document_repo.get_or_error(document_id)
    except DoesNotExist:
        raise HTTPException(
            status_code=404, detail=f"Document({document_id}) not found"
        )

    if document.available:
        raise HTTPException(
            status_code=400, detail="Document is published (available)."
        )

    chunks = await chunk_repo.import_from_markdown(document_id, content)
    return ChunksListResponse(
        chunks=[
            ChunkResponse(
                chunk_id=chunk.chunk_id,
                content=chunk.content,
                created_at=chunk.created_at,
                updated_at=chunk.updated_at,
                document=document_id,
            )
            for chunk in chunks
        ],
        total=len(chunks),
    )


# TODO 编辑文档的Tag
@admin_router.put(
    "/documents/{document_id}",
    description="用于更新文档的信息、权限、标签及设置是否作为QA文档",
)
async def update_document(
        document_id: int,
        params: UpdateDocumentRequest = Body(...),
        document_repo: PolicyDocumentRepository = get_repository_dep(
            PolicyDocumentRepository
        ),
):
    # 验证permission_role是否合法
    if params.permission_role is not None:
        if params.permission_role not in USER_ROLE_PERMISSION_MAP.values():
            raise HTTPException(status_code=400, detail="Invalid permission_role value")
    try:
        await document_repo.update(document_id, params)
        return {"message": "Document updated"}
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Invalid parameters")
    except DoesNotExist:
        raise HTTPException(
            status_code=404, detail=f"Document({document_id}) not found"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Update document error: {e}")


@admin_router.get(
    "/documents",
    response_model=DocumentsListResponse,
    tags=["admin"],
)
async def list_documents(
        skip: int = Query(0, ge=0),
        limit: int = Query(10, ge=1),
        document_repo=get_repository_dep(PolicyDocumentRepository),
):
    documents = await document_repo.fetch_all(
        document_repo.list(skip=skip, limit=limit)
    )

    return DocumentsListResponse(
        documents=[
            DocumentResponse(
                document_id=doc.document_id,
                source=doc.source,
                version=doc.version,
                available=doc.available,
                permission_role=doc.permission_role.role_id
                if doc.permission_role
                else None,
                created_at=doc.created_at,
                updated_at=doc.updated_at,
                mark_as_qa=doc.mark_as_qa,
                qa_description=doc.qa_description,
            )
            for doc in documents
        ],
        total=await document_repo.count(),
    )


@admin_router.get(
    "/documents/{document_id}",
    response_model=DocumentResponse,
)
async def get_document_info(
        document_id: int,
        document_repo=get_repository_dep(PolicyDocumentRepository),
):
    try:
        document: PolicyDocument = await document_repo.get_or_error(document_id)
        return DocumentResponse(
            document_id=document.document_id,
            source=document.source,
            version=document.version,
            available=document.available,
            permission_role=document.permission_role.role_id
            if document.permission_role
            else None,
            created_at=document.created_at,
            updated_at=document.updated_at,
            mark_as_qa=document.mark_as_qa,
            qa_description=document.qa_description,
        )
    except DoesNotExist:
        raise HTTPException(
            status_code=404, detail=f"Document({document_id}) not found"
        )


@admin_router.delete("/documents/{document_id}")
async def delete_document(
        document_id: int,
        document_repo: PolicyDocumentRepository = get_repository_dep(
            PolicyDocumentRepository
        ),
):
    try:
        # 删除文档
        await document_repo.delete_document(document_id)
        return {"message": "Document deleted"}
    except DoesNotExist:
        raise HTTPException(
            status_code=404, detail=f"Document({document_id}) not found"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Delete document error: {e}")


@admin_router.get(
    "/documents/{document_id}/chunks",
    response_model=ChunksListResponse,
    tags=["admin"],
)
async def get_document_chunks(
        document_id: int,
        skip: int = Query(0, ge=0),
        limit: int = Query(10, ge=1),
        chunks_repo: PolicyChunkRepository = get_repository_dep(PolicyChunkRepository),
):
    chunks_query = chunks_repo.list_with_document_id(
        document_id=document_id, skip=skip, limit=limit
    )
    total = await chunks_repo.count_with_document_id(document_id=document_id)

    chunks = [
        ChunkResponse(
            chunk_id=chunk.chunk_id,
            content=chunk.content,
            document=chunk.document.document_id,
            created_at=chunk.created_at,
            updated_at=chunk.updated_at,
        )
        for chunk in await chunks_repo.fetch_all(chunks_query)
    ]

    return ChunksListResponse(total=total, chunks=chunks)


@admin_router.post(
    "/documents/{document_id}/chunks", response_model=ChunkResponse, tags=["admin"]
)
async def create_chunk(
        document_id: int,
        content: str = Body(..., description="文档切块的内容", embed=True),
        chunks_repo: PolicyChunkRepository = get_repository_dep(PolicyChunkRepository),
):
    try:
        document = await chunks_repo.get_or_error(document_id)
    except DoesNotExist:
        raise HTTPException(
            status_code=404, detail=f"Document({document_id}) not found"
        )

    try:
        params = ChunkCreate(
            content=content,
            document=document_id,
            embedding=await embedding_client.embed_passage([content]),
        )
        chunk = await chunks_repo.create(params)
        return ChunkResponse(
            chunk_id=chunk.chunk_id,
            content=chunk.content,
            document=chunk.document.document_id,
            created_at=chunk.created_at,
            updated_at=chunk.updated_at,
        )
    except IntegrityError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid document_id or missing required fields: {str(e)}",
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Create chunk error: {str(e)}")


@admin_router.put("/documents/{document_id}/chunks/{chunk_id}", tags=["admin"])
async def update_chunk(
        document_id: int,
        chunk_id: int,
        content: str = Body(..., description="文档切块的内容", embed=True),
        chunks_repo: PolicyChunkRepository = get_repository_dep(PolicyChunkRepository),
):
    try:
        await chunks_repo.update(
            chunk_id,
            ChunkUpdate(
                content=content,
                embedding=await embedding_client.embed_passage([content]),
            ),
        )
        return {"message": "success"}
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="Chunk not found")
    except IntegrityError as e:
        raise HTTPException(status_code=400, detail=f"Invalid parameters: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Update chunk error: {str(e)}")


@admin_router.delete("/documents/{document_id}/chunks/{chunk_id}", tags=["admin"])
async def delete_chunk(
        document_id: int,
        chunk_id: int,
        chunks_repo: PolicyChunkRepository = get_repository_dep(PolicyChunkRepository),
):
    try:
        await chunks_repo.delete(chunk_id)
        return {"message": "Chunk deleted successfully"}
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="Chunk not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Delete chunk error: {str(e)}")
