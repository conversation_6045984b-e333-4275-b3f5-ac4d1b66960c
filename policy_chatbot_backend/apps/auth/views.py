from typing import Annotated
from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import OAuth2PasswordRequestForm
from starlette import status

from apps.auth.auth import OAuth2TokenData, create_access_token, get_current_user, get_user_manager_dep
from apps.auth.exceptions import UserNotExists, InvalidPasswordException
from apps.auth.user_manager import UserManager, UserData

auth_router = APIRouter(tags=["auth"])


@auth_router.post("/login", response_model=OAuth2TokenData)
async def login(
    data: Annotated[OAuth2PasswordRequestForm, Depends()], user_manager: UserManager = get_user_manager_dep()
) -> OAuth2TokenData:
    try:
        user = await user_manager.login(username=data.username, password=data.password)
    except UserNotExists:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    except InvalidPasswordException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=e.reason)

    return create_access_token(data=user)


@auth_router.get("/logout")
async def logout(user: UserData = Depends(get_current_user)):
    # TODO 登出逻辑，考虑到Bearer Token需要黑名单
    return {"message": "Logout success"}
