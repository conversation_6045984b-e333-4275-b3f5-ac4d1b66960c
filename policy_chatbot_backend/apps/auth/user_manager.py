import dataclasses
import json
import re
from typing import Optional

from pydantic import BaseModel

from apps.auth.exceptions import UserAlreadyExists, InvalidPasswordException, UserNotExists
from apps.auth.password import PasswordHelper
from apps.shared.model.user import User, UserInfo
from apps.shared.repository.user import UserRepository, UserInfoRepository, UserActionLogRepository
from apps.shared.schema.user import UserCreate, UserAuthProvider, UserUpdate, UserInfoCreate


class RegisterUserSchema(BaseModel):
    username: str
    password: str
    role_id: Optional[int] = None
    # TODO add more


class UserRoleData(BaseModel):
    role_id: int
    role_code: str
    permissions: list[str]


class UserData(BaseModel):
    user_id: int
    username: str
    role: Optional[UserRoleData] = None
    tags: list[str] = []


@dataclasses.dataclass
class UserManager:
    user_repo: UserRepository = None
    user_info_repo: UserInfoRepository = None
    user_action_repo: UserActionLogRepository = None
    password_hasher = PasswordHelper()

    @staticmethod
    async def validate_password(pwd: str):
        # 密码规则：8-20位，包含大小写字母、数字和特殊字符
        pattern = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$"
        if not re.match(pattern, pwd):
            raise InvalidPasswordException(
                "密码需8-20位，包含大小写字母、数字和特殊字符(@$!%*?&)"
            )

    async def create(self, data: RegisterUserSchema) -> UserData:
        user = await self.user_repo.get_user_by_username(username=data.username)
        if user:
            raise UserAlreadyExists()

        await self.validate_password(data.password)

        user = await self.user_repo.create(
            UserCreate(
                username=data.username,
                hashed_password=self.password_hasher.hash(data.password),
                cas_uuid=None,
                auth_providerL=UserAuthProvider.LOCAL.value
            )
        )

        await self.user_info_repo.create(
            UserInfoCreate(
                user=user.user_id,
                admission_year=None,
                birth_year=None,
                department=None,
                major=None,
            )
        )

        return await self._to_user_data(user)

    async def get_user_info(self, user_id: int) -> UserInfo:
        return await self.user_info_repo.get_or_none(user=user_id)

    async def _to_user_data(self, user: User) -> UserData:
        role = user.role
        tags = []
        if info := await self.get_user_info(user_id=user.user_id):
            tags.extend(
                [info.department, info.major, info.admission_year]
            )
        return UserData(user_id=user.user_id, username=user.username, tags=tags,
                        role=UserRoleData(
                            role_id=role.role_id,
                            role_code=role.role_code,
                            permissions=json.dumps(role.permissions) if role.permissions else [],
                        ) if role else None)

    async def get_by_username(self, username: str) -> UserData:
        user = await self.user_repo.get_user_by_username(username=username)
        if user is None:
            raise UserNotExists()
        return await self._to_user_data(user)

    async def login(self, username, password) -> UserData:
        """
        本地用户系统登录，只能通过本地的用户+密码
        """
        user = await self.user_repo.get_user_by_username(username=username)
        if user is None:
            raise UserNotExists()

        verified, new_hashed_pwd = self.password_hasher.verify_and_update(password, user.hashed_password)
        if not verified:
            raise InvalidPasswordException("用户密码错误")

        if new_hashed_pwd:
            await self.user_repo.update(user.user_id, UserUpdate(hashed_password=new_hashed_pwd))

        return await self._to_user_data(user)
