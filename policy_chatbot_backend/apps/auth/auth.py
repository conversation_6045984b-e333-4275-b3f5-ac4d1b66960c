import sys
from datetime import timedelta, datetime, UTC
from enum import Enum
from functools import lru_cache
from typing import Optional

from fastapi import Depends, HTTPException
from fastapi.params import Depends
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import jwt, J<PERSON><PERSON><PERSON>r
from pydantic import BaseModel
from starlette import status
from apps.auth.user_manager import UserD<PERSON>, UserManager
from apps.shared.repository.user import UserRepository, UserInfoRepository, UserActionLogRepository
from core.depends import get_repository_dep
from settings import settings

# 安全配置参数
SECRET_KEY = settings.AUTH_SECRET_KEY
ALGORITHM = settings.AUTH_ENCODE_ALGORITHM
ACCESS_TOKEN_EXPIRE_HOURS = settings.AUTH_ACCESS_TOKEN_EXPIRE_HOURS

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=settings.AUTH_TOKEN_URL)


class OAuth2TokenData(BaseModel):
    access_token: str
    token_type: str = "Bearer"


def create_access_token(data: UserData, expires_delta: Optional[timedelta] = None) -> OAuth2TokenData:
    to_encode = data.model_dump()
    expire = datetime.now(UTC) + (expires_delta or timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS))
    to_encode.update({"expire": str(expire)})
    access_token = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return OAuth2TokenData(access_token=access_token)


@lru_cache
def get_user_manager_dep():
    def _get(user_repo=get_repository_dep(UserRepository),
             info_repo=get_repository_dep(UserInfoRepository),
             action_repo=get_repository_dep(UserActionLogRepository)) -> UserManager:
        return UserManager(user_repo=user_repo, user_info_repo=info_repo, user_action_repo=action_repo)

    return Depends(_get)


async def get_current_user(token: str = Depends(oauth2_scheme), user_manager: UserManager = get_user_manager_dep()):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_data = UserData(**payload)
        if not user_data.username:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = await user_manager.get_by_username(username=user_data.username)
    if user is None:
        raise credentials_exception
    return user


class UserRoleCode(Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    TEACHER = "teacher"
    STUDENT = "student"


USER_ROLE_PERMISSION_MAP = {
    UserRoleCode.ADMIN.value: 1,
    UserRoleCode.MANAGER.value: 2,
    UserRoleCode.TEACHER.value: 3,
    UserRoleCode.STUDENT.value: 4,
}


def parse_user_role_permission_id(role_code: str) -> int:
    if role_code and role_code in USER_ROLE_PERMISSION_MAP:
        return USER_ROLE_PERMISSION_MAP[role_code]
    return sys.maxsize  # 其他则默认为无穷小权限，需要根据已有权限来判断


class RoleScopeChecker:
    """
    用户权限检查，目前仅考虑用户组权限，不考虑细粒度的功能权限
    """

    def __init__(self, required_role: Optional[str | UserRoleCode]):
        if isinstance(required_role, UserRoleCode):
            required_role = required_role.value
        self.required_role = required_role
        self.required_role_id = parse_user_role_permission_id(self.required_role)

    def __call__(self, user: UserData = Depends(get_current_user)):
        if not self.required_role:
            return

        user_role_permission_id = parse_user_role_permission_id(user.role.role_code)
        # 权限id越小则权限越大
        if user_role_permission_id <= self.required_role_id:
            return

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions",
            headers={"WWW-Authenticate": f"Bearer role={self.required_role}"},
        )
