import logging
import traceback
from enum import Enum
from typing import As<PERSON><PERSON><PERSON>ator, <PERSON>V<PERSON>, Generic, Optional

from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from langchain_core.messages import BaseMessage, BaseMessageChunk
from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI
from langchain_openai.chat_models.base import BaseChatOpenAI
from pydantic import BaseModel

from core.async_utils import to_async_io
from core.database import with_db_context

from core.logger import get_logger
from core.models import LLMInvokeLog
from settings import settings

logger = get_logger("LLM-Base")

if settings.LLM_IS_DEEPSEEK_REASONER:  # 需要让其解析ReasonContent
    _chat_model = ChatDeepSeek(
        api_base=str(settings.CHAT_LLM.API_BASE_URL),
        api_key=settings.CHAT_LLM.API_KEY,
        model=settings.CHAT_LLM.MODEL,
        temperature=settings.CHAT_LLM.TEMPERATURE,
    )
else:
    _chat_model = ChatOpenAI(
        base_url=str(settings.CHAT_LLM.API_BASE_URL),
        api_key=settings.CHAT_LLM.API_KEY,
        model=settings.CHAT_LLM.MODEL,
        temperature=settings.CHAT_LLM.TEMPERATURE,
    )

_normal_model = ChatOpenAI(
    base_url=str(settings.NORMAL_LLM.API_BASE_URL),
    api_key=settings.NORMAL_LLM.API_KEY,
    model=settings.NORMAL_LLM.MODEL,
    temperature=settings.NORMAL_LLM.TEMPERATURE,
)


class LLMModelType(Enum):
    CHAT = "chat"
    NORMAL = "normal"


LLM_MODELS = {
    LLMModelType.CHAT: _chat_model,
    LLMModelType.NORMAL: _normal_model,
}


def get_llm_model(model_type: LLMModelType) -> BaseChatOpenAI:
    return LLM_MODELS[model_type]


def get_normal_model():
    return get_llm_model(LLMModelType.NORMAL)


def get_chat_model():
    return get_llm_model(LLMModelType.CHAT)


async def call_llm(prompt: str, model: BaseChatOpenAI = get_normal_model(), **params) -> str | None:
    prompt = PromptTemplate(input_variables=list(params.keys()), template=prompt)

    try:
        response = (
            await model.ainvoke(
                await prompt.ainvoke(params),
            )
        ).content
        await record_llm_invoke(
            function="core.agents.base.call_llm",
            model=settings.LLM_MODEL,
            prompt=prompt,
            params=params,
            response=response,
        )
        return response
    except Exception as e:
        logging.error(f"call_llm({prompt}, {params}) error:", e)
        logging.error(traceback.format_exc())
        return None


StructuredOutputType = TypeVar("StructuredOutputType", bound=BaseModel)


async def call_llm_structured(
        prompt: str, output_model: Generic[StructuredOutputType], model: BaseChatOpenAI = get_normal_model(), **params
) -> StructuredOutputType | None:
    output_parser = PydanticOutputParser(pydantic_object=output_model)
    prompt = PromptTemplate(
        input_variables=list(params.keys()),
        template=f"""{prompt}
                            
                            # Output JSON Schema
                            {{format_instructions}}
""",
        partial_variables={
            "format_instructions": output_parser.get_format_instructions()
        },
    )

    chain = prompt | model | output_parser
    try:
        response = await chain.ainvoke(params)
        await record_llm_invoke(
            function="core.agents.base.call_llm",
            model=settings.LLM_MODEL,
            prompt=prompt,
            params=params,
            response=response,
        )
        return response
    except Exception as e:
        logger.error(f"call_llm_structured({prompt}, {params}) error:{e}")
        logger.error(traceback.format_exc())
        return None


class LLMChatResponse(BaseModel):
    '''
    llm_chat的响应返回，无论是否是stream模式，均使用此类作为响应输出
    '''
    content: Optional[str] = None
    reasoning_content: Optional[str] = None

    @property
    def is_reansoning(self) -> bool:
        return self.reasoning_content is not None

    @classmethod
    def from_langchain_response(cls, response: BaseMessage | BaseMessageChunk) -> "LLMChatResponse":
        content = None
        reasoning = None
        if response.content is not None and response.content != "":
            content = response.content
        if response.additional_kwargs is not None and "reasoning_content" in response.additional_kwargs:
            reasoning = response.additional_kwargs["reasoning_content"]
        return cls(content=content, reasoning_content=reasoning)


async def llm_chat(
        prompt: str, stream: bool = False, model: BaseChatOpenAI = get_chat_model(), **params
) -> LLMChatResponse | AsyncGenerator[LLMChatResponse, None] | None:
    """
    支持流式响应的LLM调用函数

    :param prompt: 输入的提示模板
    :param stream: 是否启用流式响应（默认False）
    :param model: 调用的LLM Model
    :param params: 模板参数
    :return: 字符串（非流式）或异步生成器（流式）
    """
    prompt_template = PromptTemplate(
        input_variables=list(params.keys()), template=prompt
    )

    try:
        if stream:
            async def stream_generator():
                full_response = {
                    'content': [],
                    'reasoning_content': []
                }
                async for chunk in model.astream(
                        await prompt_template.ainvoke(params)
                ):
                    chat_response = LLMChatResponse.from_langchain_response(chunk)
                    if chat_response.is_reansoning and chat_response.reasoning_content is not None:
                        full_response['reasoning_content'].append(chat_response.reasoning_content)
                    elif chat_response.content is not None:
                        full_response['content'].append(chat_response.content)
                    else:
                        pass  # 可能是tool call，暂时不处理

                    yield chat_response

                full_response['content'] = "".join(full_response['content'])
                full_response['reasoning_content'] = "".join(full_response['reasoning_content'])
                # 记录完整响应日志
                await record_llm_invoke(
                    function="core.agents.base.llm_chat",
                    model=settings.LLM_MODEL,
                    prompt=prompt_template,
                    params=params,
                    response=full_response,
                )

            return stream_generator()
        else:
            response = (
                await get_normal_model().ainvoke(
                    await prompt_template.ainvoke(params),
                )
            )
            response = LLMChatResponse.from_langchain_response(response)
            await record_llm_invoke(
                function="core.agents.base.llm_chat",
                model=settings.LLM_MODEL,
                prompt=prompt_template,
                params=params,
                response=response,
            )
            return response
    except Exception as e:
        logger.error(f"llm_chat({prompt}, {params}) error: {e}")
        logger.error(traceback.format_exc())
        return None


@to_async_io
@with_db_context
def record_llm_invoke(function: str, model: str, prompt: str, params: dict, response: str):
    try:
        LLMInvokeLog(
            function=function,
            model=model,
            prompt=prompt,
            params=str(params),
            response=response,
        ).save()
    except Exception as e:
        logging.error(f"LLMInvokeLog Database Record Error: {e}", traceback.format_exc())
        return
