from typing import List

from pydantic import BaseModel, Field

from core.agents.base import call_llm, call_llm_structured


async def stepback_document_query(query, context) -> str | None:
    return await call_llm(
        prompt="""
        你是一个AI助手，负责生成更广泛、更通用的查询，以改进RAG（检索增强生成）系统中的上下文检索效果。输出为重写后的StepBack查询语句。

        # 流程
        1. 首先，思考用户到底在问什么，优化查询语句的逻辑与用词
        2. 然后，仔细查看上下文对话，补全查询语句的细节信息，保证其符合用户的意图
        3. 最后，根据思考，生成优化后的查询语句

        # 规则
        - 根据原始查询，生成一个更通用的“Step-back查询”，以帮助检索相关的背景信息。
        - 补全对话中的已知信息，让对话问题更加接近用户真正想要提问的
        - 只需要返回查询语句，不要包含任何其他内容
        - 需要保留用户问题中的相关事物、动作、背景等关键信息,具体的事物、动作名称术语不可以省略，且要保持为常用的官方说辞，尽可能地将问题描述的具体、详细
        
        对话上下文: {context}
        原始查询语句: {query}
        """,
        query=query,
        context=context,
    )


class DecomposeQueryResult(BaseModel):
    query: List[str] = Field(
        description="根据原始查询，分解为更简单的子查询，以帮助检索相关的背景信息。"
    )


async def decompose_document_query(query) -> List[str] | None:
    result = await call_llm_structured(
        prompt="""   
你是一个AI助手，负责将复杂的查询分解为更简单的子查询，以便在RAG（检索增强生成）系统中使用，并以要求的JSON格式返回结果。

# 规则
- 根据原始查询，将其分解为2-4个更简单的子查询，这些子查询的答案结合起来能够为原始查询提供全面的回答。你需要按照你认为的相关重要程度，将子查询按照重要程度降序排列。最终结果以Schema规定的JSON格式返回。
- 每个子查询需要与用户问题涉及的事物、动作等关键信息相关，可以涉及一个或多个
- 需要保留用户问题中的相关事物、动作、背景等关键信息,具体的事物、动作名称术语不可以省略，且要保持为常用的官方说辞，尽可能地将问题描述的具体、详细
- 用户提供的具体名称的事物、动作、背景至少在一个子查询中完整存在，在其他子查询中可以同义词等效替代
- 子查询的用途是拓展性思维，查询到更多相关的信息，以此为目的进行问题分解与拓展

原始查询语句: {query}

示例：
原始查询：本校本科生保研政策的具体要求和流程是怎样的？

子查询：
- 保研资格的基本条件有哪些（如成绩排名、必修课要求等）？
- 保研流程的时间节点和关键步骤是什么（如材料提交、学院审核等）？
- 学术竞赛或科研成果在保研加分中的具体认定标准是什么？
- 支教保研、竞赛保研等特殊渠道的政策与普通保研有何区别？
        """,
        output_model=DecomposeQueryResult,
        query=query,
    )
    if not result:
        return None

    return result.query
