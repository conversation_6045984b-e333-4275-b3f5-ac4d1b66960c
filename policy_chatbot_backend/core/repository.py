from typing import List, Optional, Type, Generic, TypeVar

from peewee import DoesNotExist, Model, ModelSelect
from pydantic import BaseModel

from core.async_utils import to_async_io

ModelType = TypeVar("ModelType", bound=Model)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model

    @to_async_io
    def create(self, schema: CreateSchemaType) -> ModelType:
        return self.model.create(**schema.model_dump())

    @to_async_io
    def get_or_none(self, *query, **filters) -> ModelType:
        return self.model.get_or_none(*query, **filters)

    @to_async_io
    def get_by_id(self, id) -> ModelType | None:
        try:
            return self.model.get_by_id(id)
        except self.model.DoesNotExist:
            return None

    async def get_or_error(self, id) -> ModelType:
        item = await self.get_by_id(id)
        if not item:
            raise DoesNotExist(f"{self.model.__name__} with {id} doesn't exists.")
        return item

    def list_all(self, *query, **filters) -> ModelSelect:
        sq = self.model.select()
        if query:
            # Handle simple lookup using just the primary key.
            if len(query) == 1 and isinstance(query[0], int):
                sq = sq.where(self.model._meta.primary_key == query[0])
            else:
                sq = sq.where(*query)
        if filters:
            sq = sq.filter(**filters)
        return sq

    def list(self, skip: Optional[int] = 0, limit: Optional[int] = 100, *query, **filters) -> ModelSelect:
        query = (self.list_all(*query, **filters))
        if skip:
            query = query.offset(skip)
        if limit:
            query = query.limit(limit)
        return query

    @to_async_io
    def _execute_fetch_all(self, query: ModelSelect) -> List[ModelType]:
        """异步执行查询并返回列表"""
        return list(query)

    @to_async_io
    def _execute_fetch_one(self, query: ModelSelect) -> Optional[ModelType]:
        """异步执行查询并返回单个结果"""
        try:
            return query.get()
        except DoesNotExist:
            return None

    async def fetch_all(self, query: ModelSelect) -> List[ModelType]:
        """异步获取所有结果"""
        return await self._execute_fetch_all(query)

    async def fetch_one_or_none(self, query: ModelSelect) -> Optional[ModelType]:
        """异步获取单个结果"""
        return await self._execute_fetch_one(query)

    @to_async_io
    def count_query(self, query: Optional[ModelSelect] = None) -> int:
        """异步统计数量，允许基于现有查询"""
        if query is None:
            query = self.model.select()
        return query.count()

    @to_async_io
    def count(self) -> int:
        return self.model.select().count()

    @to_async_io
    def _save_item(self, item: ModelType):
        item.save()

    @to_async_io
    def _delete_instance(self, item: ModelType):
        item.delete_instance()

    async def update(self, id, schema: UpdateSchemaType) -> ModelType:
        item = await self.get_or_error(id)
        for key, value in schema.model_dump(exclude_unset=True).items():
            setattr(item, key, value)
        await self._save_item(item)
        return item

    async def delete(self, id):
        item = await self.get_or_error(id)
        await self._delete_instance(item)
