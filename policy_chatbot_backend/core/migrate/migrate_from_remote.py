from pydantic import BaseModel
import psycopg2
import datetime


class DBConifg(BaseModel):
    host: str
    port: int
    user: str
    password: str
    database: str


source_db = DBConifg(
    host="***********",
    port=5432,
    user="root",
    password="52436e70-192f-4133-a1c4-701b27bf20be",
    database="testdb",
)

target_db = DBConifg(
    host="***********",
    port=5432,
    user="root",
    password="52436e70-192f-4133-a1c4-701b27bf20be",
    database="policy",
)


def connect_to_db(config: DBConifg):
    """连接到数据库"""
    conn = psycopg2.connect(
        host=config.host,
        port=config.port,
        user=config.user,
        password=config.password,
        database=config.database
    )
    return conn


def migrate_documents(source_conn, target_conn):
    """迁移policy_documents表数据"""
    print("开始迁移policy_documents表数据...")

    source_cursor = source_conn.cursor()
    target_cursor = target_conn.cursor()

    # 从源数据库获取数据
    source_cursor.execute("""
        SELECT document_id, source, version, created_at, available, permission_id, updated_at, tags
        FROM policy_documents
    """)
    documents = source_cursor.fetchall()

    # 插入到目标数据库
    for doc in documents:
        document_id, source, version, created_at, available, permission_id, updated_at, tags = doc

        # 在目标数据库中，permission_id 改成了 permission_role_id
        # 如果没有 updated_at，使用 created_at
        if updated_at is None:
            updated_at = created_at

        target_cursor.execute("""
            INSERT INTO policy_documents 
            (document_id, source, version, created_at, updated_at, available, permission_role_id, mark_as_qa, qa_description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (document_id) DO UPDATE SET
                source = EXCLUDED.source,
                version = EXCLUDED.version,
                created_at = EXCLUDED.created_at,
                updated_at = EXCLUDED.updated_at,
                available = EXCLUDED.available,
                permission_role_id = EXCLUDED.permission_role_id,
                mark_as_qa = EXCLUDED.mark_as_qa,
                qa_description = EXCLUDED.qa_description
        """, (
            document_id, source, version, created_at, updated_at, available, permission_id, False,
            None))  # 根据源数据实际情况调整默认值

    target_conn.commit()
    print(f"成功迁移 {len(documents)} 条policy_documents记录")


def migrate_chunks(source_conn, target_conn):
    """迁移policy_chunks表数据"""
    print("开始迁移policy_chunks表数据...")

    source_cursor = source_conn.cursor()
    target_cursor = target_conn.cursor()

    # 从源数据库获取数据
    source_cursor.execute("""
        SELECT chunk_id, content, embedding, document_id, created_at
        FROM policy_chunks
    """)
    chunks = source_cursor.fetchall()

    # 插入到目标数据库
    for chunk in chunks:
        chunk_id, content, embedding, document_id, created_at = chunk

        # 目标数据库需要 updated_at 字段，使用 created_at
        updated_at = created_at

        target_cursor.execute("""
            INSERT INTO policy_chunks 
            (chunk_id, content, embedding, document_id, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
            ON CONFLICT (chunk_id) DO UPDATE SET
                content = EXCLUDED.content,
                embedding = EXCLUDED.embedding,
                document_id = EXCLUDED.document_id,
                created_at = EXCLUDED.created_at,
                updated_at = EXCLUDED.updated_at
        """, (chunk_id, content, embedding, document_id, created_at, updated_at))

    target_conn.commit()
    print(f"成功迁移 {len(chunks)} 条policy_chunks记录")


def main():
    """主函数，执行数据迁移"""
    try:
        # 连接数据库
        source_conn = connect_to_db(source_db)
        target_conn = connect_to_db(target_db)

        print("数据库连接成功")

        # 迁移数据
        migrate_documents(source_conn, target_conn)
        migrate_chunks(source_conn, target_conn)

        print("数据迁移完成")

    except Exception as e:
        print(f"数据迁移过程中出错: {e}")
    finally:
        # 关闭连接
        if 'source_conn' in locals():
            source_conn.close()
        if 'target_conn' in locals():
            target_conn.close()


if __name__ == "__main__":
    main()
