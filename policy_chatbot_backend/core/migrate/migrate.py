import logging
import traceback
from abc import ABC, abstractmethod

from core.database import db

logger = logging.getLogger(__name__)


class BaseMigration(ABC):
    """数据库迁移基类"""

    db = db

    @property
    @abstractmethod
    def version(self) -> int:
        """迁移版本号 (格式: YYYYMMDDHH 时间戳)"""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        pass

    @abstractmethod
    def up(self):
        """执行迁移升级"""
        pass

    @abstractmethod
    def down(self):
        """执行迁移回滚"""
        pass


def migrate_db():
    logger.info("Checking DataBase Version...")

    from core.migrate.migrations import InitMigration, V2025041110Migration
    from core.models import DataBaseVersion

    DB_MIGRATE_VERSIONS = [V2025041110Migration()]

    sorted_migrations = sorted(DB_MIGRATE_VERSIONS, key=lambda x: x.version)

    if not DataBaseVersion.table_exists():
        db.create_tables([DataBaseVersion])
        logger.info("Created data_base_version table")
        cur_db_version = -1
        # 第一次建表直接初始化就完事
        sorted_migrations = [InitMigration()]
    else:
        cur_db_version = (
            DataBaseVersion.select()
            .limit(1)
            .order_by(DataBaseVersion.version_code.desc())
            .get()
            .version_code
        )

    pending_migrations = [
        migration
        for migration in sorted_migrations
        if migration.version > cur_db_version
    ]

    upgraded_migrations = []

    for migration in pending_migrations:
        try:
            logger.info(f"Running migration {migration.version}")
            migration.up()

            upgraded_migrations.append(migration)

            DataBaseVersion.create(
                version_code=migration.version, description=migration.description
            )

            logger.info(f"Migration {migration.version} completed")
        except Exception as e:
            logger.error(f"Migration {migration.version} failed: {e}")
            logger.error(traceback.format_exc())

            for m in reversed(upgraded_migrations):
                m.down()

            raise e
