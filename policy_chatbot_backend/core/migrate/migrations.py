from datetime import datetime

from apps.auth.password import PasswordHelper
from core.migrate.migrate import BaseMigration
from core.models import LLMInvokeLog
from apps.shared.model.policy import (
    PolicyDocument,
    PolicyChunk,
)
from apps.shared.model.chat import ChatThread, ChatMessage, ChatFeedback
from apps.shared.model.user import User
from apps.shared.model.policy import (
    PolicyTag,
    PolicyGrade,
    PolicyDocumentGrade,
    PolicyDocumentTag,
)
from apps.shared.model.user import UserRole, UserInfo, UserActionLog
from settings import settings
from peewee import BooleanField, TextField


class InitMigration(BaseMigration):
    """
    第一次建表不需要考虑之前的版本迁移情况，直接创建最新的。本系统不保存最初的实现，不保证数据表可溯洄
    ALWAYS TODO 保持本迁移为最新的实现
    """

    @property
    def version(self) -> int:
        return int(datetime.now().strftime("%Y%m%d%H"))

    @property
    def description(self) -> str:
        return """Init"""

    def _init_user(self):
        # 这四个用户角色严禁更改和删除
        UserRole.create(role_id=1, role_code="admin")
        UserRole.create(
            role_id=2, role_code="manager", permissions='["policy","feedback"]'
        )
        UserRole.create(role_id=3, role_code="teacher", permissions="[]")
        UserRole.create(role_id=4, role_code="student", permissions="[]")

        User.create(
            user_id=1000,
            username=settings.user_config.ADMIN_USERNAME,
            hashed_password=PasswordHelper().hash(settings.user_config.ADMIN_PASSWORD),
            nickname="管理员",
            role=1,
        )

    def up(self):
        self.db.create_tables(
            [
                UserRole,
                User,
                UserInfo,
                UserActionLog,
            ]
        )

        self._init_user()

        self.db.create_tables(
            [
                PolicyTag,
                PolicyGrade,
                PolicyDocument,
                PolicyDocumentGrade,
                PolicyDocumentTag,
                PolicyChunk,
            ]
        )

        self.db.create_tables([ChatThread, ChatMessage, ChatFeedback])

        self.db.create_tables([LLMInvokeLog])

        self.db.execute_sql(
            "SELECT setval('policy_chunks_chunk_id_seq', (SELECT MAX(chunk_id) FROM policy_chunks));"
        )
        self.db.execute_sql(
            "SELECT setval('policy_documents_document_id_seq', (SELECT MAX(document_id) FROM policy_documents));"
        )

    def down(self):
        pass


class V2025041110Migration(BaseMigration):
    @property
    def version(self) -> int:
        return 2025041110

    @property
    def description(self) -> str:
        return "迁移QA文档相关字段"

    def up(self):
        self.db.execute_sql(
            "ALTER TABLE policy_documents ADD COLUMN mark_as_qa BOOLEAN NOT NULL DEFAULT FALSE"
        )
        self.db.execute_sql(
            "ALTER TABLE policy_documents ADD COLUMN qa_description TEXT"
        )

    def down(self):
        self.db.execute_sql("ALTER TABLE policy_documents DROP COLUMN mark_as_qa")
        self.db.execute_sql("ALTER TABLE policy_documents DROP COLUMN qa_description")
