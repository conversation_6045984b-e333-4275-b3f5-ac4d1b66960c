import logging
from functools import wraps
from playhouse.pool import PooledPostgresqlExtDatabase

from core.logger import get_logger
from settings import settings

logger = get_logger(__name__)

db = PooledPostgresqlExtDatabase(
    settings.DB_NAME,
    user=settings.DB_USERNAME,
    password=settings.DB_PASSWORD,
    host=settings.DB_HOST,
    port=settings.DB_PORT,
    max_connections=settings.DB_MAX_CONNECTION,
    stale_timeout=60,
)

logger.info(f"Connected to database {settings.DB_NAME} ({settings.DB_HOST}:{settings.DB_PORT})")


def with_db_context(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        with db.connection_context():
            return func(*args, **kwargs)

    logger.debug(f"{func.__name__} with db context")

    return wrapper


def initialize_database():
    logger.info("Initializing database config...")

    # 启用 pgvector 扩展
    db.execute_sql("CREATE EXTENSION IF NOT EXISTS vector")

    logger.info("Migrating database...")

    from core.migrate.migrate import migrate_db

    with db.atomic() as transaction:
        try:
            migrate_db()
        except Exception as e:
            transaction.rollback()
            logger.error(f"Failed to migrate database: {e}")
            raise e


def shutdown_database():
    if db and not db.is_closed():
        db.close()
        logger.debug("Database closed")
