import logging
import os
from functools import lru_cache

from settings import settings

formatter = logging.Formatter(
    fmt="[%(asctime)s] - (%(name)s) - %(levelname)s => %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


@lru_cache
def _get_file_handler(name: str) -> logging.FileHandler:
    """
    全局通过cache共享指向同一个log文件的FileHandler，避免线程安全问题
    """
    file_handler = logging.FileHandler("logs/backend.log")
    file_handler.setLevel(logging.INFO if settings.DEBUG_MODE else logging.WARNING)
    file_handler.setFormatter(formatter)
    return file_handler


def _init_logger(logger: logging.Logger, name: str) -> logging.Logger:
    # 初始化log目录
    if not os.path.exists("logs"):
        os.makedirs("logs")

    log_level = logging.DEBUG if settings.DEBUG_MODE else logging.INFO
    logger.setLevel(log_level)

    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setLevel(logging.DEBUG)
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        if settings.LOG_TO_FILE:
            logger.addHandler(_get_file_handler(name))

    return logger


def get_logger(name: str) -> logging.Logger:
    logger = logging.getLogger(name)
    return _init_logger(logger, name=name)


def init_logger():
    logger = logging.getLogger()
    _init_logger(logger, name=logger.name)
