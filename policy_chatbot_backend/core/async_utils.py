import asyncio
from functools import wraps
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
import os
from typing import Optional, Callable, Any

# 全局执行器实例
_thread_executor: Optional[ThreadPoolExecutor] = None
_process_executor: Optional[ProcessPoolExecutor] = None


def init_executors(
    io_max_workers: Optional[int] = None, cpu_max_workers: Optional[int] = None
) -> None:
    """
    初始化全局线程池和进程池执行器

    :param io_max_workers: IO 线程池最大工作线程数 (默认自动计算)
    :param cpu_max_workers: CPU 进程池最大工作进程数 (默认 CPU 核心数)
    """
    global _thread_executor, _process_executor

    if _thread_executor is None:
        _thread_executor = ThreadPoolExecutor(max_workers=io_max_workers)

    if _process_executor is None:
        cpu_max_workers = cpu_max_workers or (os.cpu_count() or 1)
        _process_executor = ProcessPoolExecutor(max_workers=cpu_max_workers)


def shutdown_executors() -> None:
    """安全关闭所有执行器"""
    global _thread_executor, _process_executor

    if _thread_executor:
        _thread_executor.shutdown(wait=True)
        _thread_executor = None

    if _process_executor:
        _process_executor.shutdown(wait=True)
        _process_executor = None


def to_async_io(func: Callable) -> Callable:
    """
    装饰器：将同步 IO 密集型函数转换为异步函数，使用线程池执行

    使用示例：
    @io_bound
    def your_sync_io_function(...):
        ...
    """

    @wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        if _thread_executor is None:
            raise RuntimeError(
                "Thread executor not initialized. Call init_executors() first."
            )

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _thread_executor, lambda: func(*args, **kwargs)
        )

    return wrapper


def to_async_cpu(func: Callable) -> Callable:
    """
    装饰器：将同步 CPU 密集型函数转换为异步函数，使用进程池执行

    使用示例：
    @cpu_bound
    def your_sync_cpu_function(...):
        ...
    """

    @wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        if _process_executor is None:
            raise RuntimeError(
                "Process executor not initialized. Call init_executors() first."
            )

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            _process_executor, lambda: func(*args, **kwargs)
        )

    return wrapper


async def run_io(func: Callable, *args, **kwargs) -> Any:
    """
    直接执行同步 IO 密集型函数（无需装饰器）

    使用示例：
    result = await run_io(your_sync_io_function, arg1, arg2...)
    """
    if _thread_executor is None:
        raise RuntimeError(
            "Thread executor not initialized. Call init_executors() first."
        )

    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(_thread_executor, lambda: func(*args, **kwargs))


async def run_cpu(func: Callable, *args, **kwargs) -> Any:
    """
    直接执行同步 CPU 密集型函数（无需装饰器）

    使用示例：
    result = await run_cpu(your_sync_cpu_function, arg1, arg2...)
    """
    if _process_executor is None:
        raise RuntimeError(
            "Process executor not initialized. Call init_executors() first."
        )

    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(_process_executor, lambda: func(*args, **kwargs))
