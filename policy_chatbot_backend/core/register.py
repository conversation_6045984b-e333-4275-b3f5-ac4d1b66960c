import os

from fastapi import <PERSON>AP<PERSON>
from fastapi.concurrency import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware

from apps.admin.views import admin_router
from apps.auth.views import auth_router
from apps.chat.views import chat_router
from apps.user.views import user_router
from core.async_utils import init_executors, shutdown_executors
from core.database import initialize_database, shutdown_database
from core.logger import init_logger


def register_app() -> FastAPI:
    app = FastAPI(lifespan=lifespan, max_size=50 * 1024 * 1024)
    register_middleware(app)

    def include_api_v1_router(router, prefix=""):
        app.include_router(router, prefix=f"/api/v1{prefix}")

    include_api_v1_router(chat_router)
    include_api_v1_router(admin_router, prefix="/admin")
    include_api_v1_router(auth_router, prefix="/auth")
    include_api_v1_router(user_router, prefix="/user")

    return app


@asynccontextmanager
async def lifespan(app: FastAPI):
    init_logger()
    init_executors(io_max_workers=50, cpu_max_workers=os.cpu_count())
    initialize_database()

    yield

    shutdown_database()
    shutdown_executors()


def register_middleware(app: FastAPI):
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
