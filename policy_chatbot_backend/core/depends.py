from functools import lru_cache
from typing import Type, TypeVar
from fastapi import Depends
from core.database import with_db_context
from core.repository import BaseRepository
from core.database import db

RepositoryType = TypeVar("RepositoryType", bound=BaseRepository)


@lru_cache
def get_repository_dep(repo_type: Type[RepositoryType]):
    @with_db_context
    def _get_repo() -> RepositoryType:
        return repo_type()

    return Depends(_get_repo)


def get_db_session():
    return db
