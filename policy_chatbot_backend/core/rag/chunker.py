import json
import re
from typing import List
from chonkie import Semantic<PERSON><PERSON><PERSON>
from chonkie import BaseEmbeddings
import requests
from langchain_text_splitters import MarkdownHeaderTextSplitter

from core.async_utils import to_async_io
from core.logger import get_logger
from settings import settings

import numpy as np

logger = get_logger("chunker")


class JinaEmbeddings(BaseEmbeddings):
    api_key = settings.EMBEDDING_KEY
    model = settings.EMBEDDING_MODEL
    dimensions = settings.EMBEDDING_DIMENSIONS

    def __init__(self):
        super().__init__()
        self.url = settings.EMBEDDING_URL
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

    def embed(self, text: str) -> np.ndarray:
        """Embed a single text string into a vector representation."""
        response = self.get_embeddings("retrieval.passage", [text])
        return np.array(response[0]["embedding"])

    def embed_batch(self, texts: List[str]) -> List[np.ndarray]:
        """Embed a list of text strings into vector representations."""
        response = self.get_embeddings("retrieval.passage", texts)
        return [np.array(item["embedding"]) for item in response]

    def count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        # Implement token counting logic here
        # For simplicity, we'll use a placeholder function
        return len(text) * 1.2

    def count_tokens_batch(self, texts: List[str]) -> List[int]:
        """Count the number of tokens in a list of text strings."""
        return [self.count_tokens(text) for text in texts]

    def get_embeddings(
            self,
            task,
            input_texts,
            late_chunking=False,
            dimensions=settings.EMBEDDING_DIMENSIONS,
            embedding_type="ubinary",
    ):
        """
        获取文本的嵌入向量。

        :param model: 使用的模型名称，例如 "jina-embeddings-v3"
        :param task: 任务类型，例如 "retrieval.passage"
        :param input_texts: 输入的文本列表
        :param late_chunking: 是否延迟分块，默认为 False
        :param dimensions: 嵌入向量的维度，默认为 1024
        :param embedding_type: 嵌入类型，默认为 "ubinary"
        :return: API 的响应内容
        """
        data = {
            "model": self.model,
            "task": task,
            "late_chunking": late_chunking,
            "dimensions": dimensions,
            "embedding_type": embedding_type,
            "input": input_texts,
        }

        response = requests.post(self.url, headers=self.headers, data=json.dumps(data))
        if response.status_code == 200:
            return response.json()["data"]
        else:
            logger.error(
                f"JinaEmbeddingClient Error when embedding {input_texts} with status code {response.status_code}")
            raise Exception(
                f"API request failed with status code {response.status_code}: {response.text}"
            )

    @property
    def dimension(self) -> int:
        """Return the dimension of the embedding vectors."""
        return self.dimensions


def is_markdown(text):
    # 定义一系列常见 Markdown 语法的正则表达式
    patterns = [
        r"^\s*#{1,6}\s+",  # Markdown 标题，如 "# Title"
        r"\[.*?\]\(.*?\)",  # 链接，如 "[text](url)"
        r"\*\*.*?\*\*",  # 粗体，如 "**bold**"
        r"\*.*?\*",  # 斜体，如 "*italic*"
        r"`{1,3}.*?`{1,3}",  # 行内代码或代码块，如 "`code`" 或 "```code```"
    ]

    # 如果文本中有任一模式匹配，则可以初步认为文本可能为 Markdown 格式
    for pattern in patterns:
        if re.search(pattern, text, re.MULTILINE):
            return True
    return False


_markdown_header_to_split_on = [
    ("#", "Header 1"),
    ("##", "Header 2"),
    ("###", "Header 3"),
]
markdown_chunker = MarkdownHeaderTextSplitter(
    headers_to_split_on=_markdown_header_to_split_on
)

paragraph_chunker = SemanticChunker(
    embedding_model=JinaEmbeddings(),  # Default model
    threshold=0.55,  # Similarity threshold (0-1) or (1-100) or "auto"
    chunk_size=512,  # Maximum tokens per chunk
    min_sentences=1,  # Initial sentences per chunk
    delim=["\n"],
    mode="cumulative",
)

sentence_chunker = SemanticChunker(
    embedding_model=JinaEmbeddings(),  # Default model
    threshold=0.7,  # Similarity threshold (0-1) or (1-100) or "auto"
    chunk_size=512,  # Maximum tokens per chunk
    min_sentences=1,  # Initial sentences per chunk
    delim=["。", "\n"],
)


def chapter_metadata_to_title(metadata: dict) -> str:
    title = ""
    for key, value in metadata.items():
        title += value + " "
    return title


@to_async_io
def chunk_document(text: str) -> List[str]:
    """
    Return:
        List of chunks

        Chunk:
            text: str   markdown text
            token_count: int
            sentences: List[str]
    """
    paragraphs = []
    if is_markdown(text):
        chapters = markdown_chunker.split_text(text)
        for chapter in chapters:
            para_header_text = chapter_metadata_to_title(chapter.metadata).replace(
                "\n", " "
            )
            paras = [
                rf"[{para_header_text}]{chunk.text}"
                for chunk in paragraph_chunker.chunk(chapter.page_content)
                if chunk.text.strip()
            ]
            paras_with_len_checked = []
            for para in paras:
                if len(para) > 1000:
                    sentences = sentence_chunker.chunk(para)
                    paras_with_len_checked.extend(sentences)
                else:
                    paras_with_len_checked.append(para)
            paragraphs.extend(paras_with_len_checked)
    else:
        paras = [
            chunk.text for chunk in paragraph_chunker.chunk(text) if chunk.text.strip()
        ]
        paras_with_len_checked = []
        for para in paras:
            if len(para) > 1000:
                sentences = sentence_chunker.chunk(para)
                paras_with_len_checked.extend(sentences)
            else:
                paras_with_len_checked.append(para)
        paragraphs.extend(paras_with_len_checked)
    return paragraphs
