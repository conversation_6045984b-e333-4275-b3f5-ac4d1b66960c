from typing import List, Any
import requests
from core.async_utils import to_async_io
from core.logger import get_logger
from settings import settings

logger = get_logger(__name__)


class RerankClient:
    def __init__(self):
        self.model = settings.RERANK_MODEL
        self.url = settings.RERANK_URL
        self.key = settings.RERANK_KEY

    @to_async_io
    def rerank(
            self,
            query: str,
            documents: List[Any],
            top_n: int = 4,
            return_documents: bool = True,
            max_chunks_per_doc: int = 2048,
    ) -> List | None:
        headers = {
            "Authorization": f"Bearer {self.key}",
            "Content-Type": "application/json",
        }
        data = {
            "model": self.model,
            "query": query,
            "documents": documents,
            "top_n": top_n,
            "return_documents": False,
            "max_chunks_per_doc": max_chunks_per_doc,
        }
        response = requests.post(self.url, headers=headers, json=data)

        if response.status_code == 200:
            data = response.json()
            rank_indexes = data["results"]
            if not isinstance(rank_indexes, list):
                logger.error(f"rerank response is not a list: {data}")
                return None

            if return_documents:
                return [documents[item["index"]] for item in rank_indexes]

            return [item["index"] for item in rank_indexes]

        logger.error(f"rerank request failed : ({response.status_code}) {response}")
        return None


rerank_client = RerankClient()
