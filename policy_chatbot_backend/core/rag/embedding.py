from abc import ABC
from typing import List
import numpy as np
import requests
import json
from core.async_utils import to_async_io
from settings import settings
from core.logger import get_logger

logger = get_logger(__name__)

class EmbeddingClient(ABC):
    async def embed_passage(self, input_texts: List[str]) -> List:
        pass

    async def embed_query(self, input_texts: List[str]) -> List:
        pass

    async def get_embeddings(
        self,
        task,
        input_texts,
        late_chunking=False,
        dimensions=settings.EMBEDDING_DIMENSIONS,
        embedding_type="float",
    ) -> List:
        pass


class JinaEmbeddingsClient(EmbeddingClient):
    url = settings.EMBEDDING_URL
    api_key = settings.EMBEDDING_KEY
    model = settings.EMBEDDING_MODEL

    def __init__(self):
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

    async def embed_passage(self, input_texts: List[str]) -> List:
        return await self.get_embeddings(
            "retrieval.passage", input_texts, late_chunking=True
        )

    async def embed_query(self, input_texts: List[str]) -> List:
        return await self.get_embeddings("retrieval.passage", input_texts)

    @to_async_io
    def get_embeddings(
        self,
        task: str,
        input_texts: List[str],
        late_chunking: bool = False,
        dimensions: int = settings.EMBEDDING_DIMENSIONS,
        embedding_type: str = "float",
    ) -> List:
        """
        获取文本的嵌入向量。

        :param model: 使用的模型名称，例如 "jina-embeddings-v3"
        :param task: 任务类型，例如 "retrieval.passage"
        :param input_texts: 输入的文本列表
        :param late_chunking: 是否延迟分块，默认为 False
        :param dimensions: 嵌入向量的维度，默认为 1024
        :param embedding_type: 嵌入类型，默认为 "ubinary"
        :return: API 的响应内容
        """
        data = {
            "model": self.model,
            "task": task,
            "late_chunking": late_chunking,
            "dimensions": dimensions,
            "embedding_type": embedding_type,
            "input": input_texts,
        }

        response = requests.post(self.url, headers=self.headers, data=json.dumps(data))
        if response.status_code == 200:
            data = response.json()["data"]
            if not data:
                return []
            embedding = data[0]["embedding"]
            if isinstance(embedding, np.ndarray):
                embedding = embedding.tolist()
            return embedding
        else:
            raise Exception(
                f"API request (data={data}) failed with status code {response.status_code}: {response.text}"
            )


embedding_client: EmbeddingClient = JinaEmbeddingsClient()
