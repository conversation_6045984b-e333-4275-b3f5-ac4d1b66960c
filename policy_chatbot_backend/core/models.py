import datetime

from peewee import Date<PERSON><PERSON>Field, CharField, TextField, AutoField, Model, IntegerField

from core.database import db


class BaseModel(Model):
    class Meta:
        database = db


class DataBaseVersion(BaseModel):
    id = AutoField()
    version_code = IntegerField(unique=True)
    description = TextField()

    class Meta:
        table_name = "data_base_version"


class LLMInvokeLog(BaseModel):
    id = AutoField()
    function = CharField(max_length=500)
    model = Char<PERSON>ield(max_length=500)
    prompt = TextField()
    params = TextField()
    response = TextField()
    created_at = DateTimeField(default=datetime.datetime.now)

    class Meta:
        table_name = "llm_invoke_log"  # 指定表名
