def dict_to_markdown(data, recursive: bool = True) -> str:
    def helper(current_data, current_level):
        parts = []
        for key, value in current_data.items():
            # 添加当前层级的标题
            parts.append(f"{'#' * current_level} {key}\n")
            if recursive and isinstance(value, dict):
                # 递归处理子字典
                parts.append(helper(value, current_level + 1))
            else:
                # 添加非字典值并后跟两个换行符
                parts.append(f"{value}\n\n")
        return "".join(parts)

    # 生成Markdown内容并去除末尾多余换行后添加单个换行符
    markdown = helper(data, 1)
    return markdown.rstrip() + "\n"
