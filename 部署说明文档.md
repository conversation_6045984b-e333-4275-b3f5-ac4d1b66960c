# 知识库问答助手 - 部署说明文档

## 项目概述

知识库问答助手采用容器化部署方案，基于 Docker Compose 编排多个服务组件。系统包含前端 Web 应用、后端 API 服务、PostgreSQL 数据库和 Nginx 网关，通过统一的容器编排实现高可用、易维护的部署架构。

## 系统架构

### 服务组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Gateway │    │  Frontend (Web) │    │ Backend (API)   │
│   Port: 80      │────│   Port: 3000    │────│   Port: 8000    │
│                 │    │   Next.js + Bun │    │   FastAPI + UV  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              ┌─────────────────┐             │
         └──────────────│  PostgreSQL DB  │─────────────┘
                        │   Port: 5432    │
                        │   + pgvector    │
                        └─────────────────┘
```

### 技术栈总览

| 组件 | 技术栈 | 版本 | 用途 |
|------|--------|------|------|
| 网关 | Nginx | Alpine | 反向代理、负载均衡、静态资源服务 |
| 前端 | Next.js + Bun | 15.3.0 | 用户界面、知识库管理、聊天交互 |
| 后端 | FastAPI + Python | 3.13 | API 服务、RAG 检索、AI 问答 |
| 数据库 | PostgreSQL + pgvector | 17 | 数据存储、向量检索 |

## 部署环境要求

### 硬件要求
- **CPU**: 4 核心以上（推荐 8 核心）
- **内存**: 8GB 以上（推荐 16GB）
- **存储**: 50GB 以上可用空间
- **网络**: 稳定的互联网连接（用于 AI 模型调用）

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+
- **Just**: 构建工具（可选，用于简化操作）

### 网络要求
- **出站访问**: 需要访问 AI 模型 API（如 DeepSeek、Jina 等）
- **端口开放**: 80 端口（HTTP 访问）
- **内网通信**: 容器间网络通信

## 部署配置

### 1. 环境变量配置

#### 后端环境变量 (`backend.env`)
```bash
# 调试模式
DEBUG_MODE=false

# LLM 配置
LLM_IS_DEEPSEEK_REASONER=true
LLM_KEY=your_deepseek_api_key
LLM_MODEL=deepseek-v3
LLM_API_BASE_URL=https://api.deepseek.com/v1
LLM_TEMPERATURE=1.0

# 聊天专用模型配置
CHAT_LLM__API_KEY=your_chat_api_key
CHAT_LLM__MODEL=deepseek-r1
CHAT_LLM__API_BASE_URL=https://api.deepseek.com/v1
CHAT_LLM__TEMPERATURE=1.0

# 普通任务模型配置
NORMAL_LLM__API_KEY=your_normal_api_key
NORMAL_LLM__MODEL=deepseek-v3
NORMAL_LLM__API_BASE_URL=https://api.deepseek.com/v1
NORMAL_LLM__TEMPERATURE=1.0

# 嵌入模型配置
EMBEDDING_URL=https://api.jina.ai/v1/embeddings
EMBEDDING_KEY=your_jina_api_key
EMBEDDING_MODEL=jina-embeddings-v3
EMBEDDING_TYPE=float
EMBEDDING_DIMENSIONS=1024

# 重排序模型配置
RERANK_MODEL=jina-reranker-v2-base-multilingual
RERANK_URL=https://api.jina.ai/v1/rerank
RERANK_KEY=your_rerank_api_key

# RAG 检索参数
RAG_RETRIEVE_TOP_K=5
RAG_RETRIEVE_SCALE_FACTOR=1.0
RAG_RETRIEVE_ALL_THRESHOLD_CHUNK_COUNT=3
RAG_RETRIEVE_ALL_MAX_WORDS=5000

# 认证配置
AUTH_SECRET_KEY=your_secret_key_here
AUTH_ENCODE_ALGORITHM=HS256
AUTH_ACCESS_TOKEN_EXPIRE_HOURS=72

# 用户配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password
```

#### Docker Compose 环境变量
```yaml
# docker-compose.yaml 中的环境变量
services:
  backend:
    environment:
      - DB_HOST=database
      - DB_PORT=5432
      - DB_NAME=policy
      - DB_USERNAME=root
      - DB_PASSWORD=92469g70-234f-1235-a1D4-314b27Hj29be
  
  frontend:
    environment:
      - NEXT_PUBLIC_BASE_URL="/api/v1"
  
  database:
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: 92469g70-234f-1235-a1D4-314b27Hj29be
      POSTGRES_DB: policy
```

### 2. Nginx 配置 (`nginx.conf`)

```nginx
user nginx;
worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志配置
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    # 性能优化
    sendfile on;
    keepalive_timeout 65;
    proxy_socket_keepalive on;
    
    server {
        listen 80;
        server_name localhost;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 后端 API 代理
        location /api {
            client_max_body_size 100M;
            proxy_pass http://backend:8000;
            proxy_buffering off;
            proxy_read_timeout 1200s;
            
            # 重定向修正
            proxy_redirect ~^(http://backend:8000)(.*)$ http://$host/api$2;
            
            # CORS 头
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        }
        
        # 前端应用代理
        location / {
            proxy_pass http://frontend:3000;
            proxy_set_header Accept-Encoding "";
        }
    }
}
```

## 构建与部署流程

### 1. 使用 Just 构建工具

#### 安装 Just
```bash
# macOS
brew install just

# Ubuntu/Debian
wget -qO - 'https://proget.makedeb.org/debian-feeds/prebuilt-mpr.pub' | gpg --dearmor | sudo tee /usr/share/keyrings/prebuilt-mpr-archive-keyring.gpg 1> /dev/null
echo "deb [arch=all,amd64,arm64 signed-by=/usr/share/keyrings/prebuilt-mpr-archive-keyring.gpg] https://proget.makedeb.org prebuilt-mpr $(lsb_release -cs)" | sudo tee /etc/apt/sources.list.d/prebuilt-mpr.list
sudo apt update
sudo apt install just
```

#### 构建命令
```bash
# 同步代码（如果使用 Git 子模块）
just sync

# 构建后端镜像
just build_backend

# 构建前端镜像
just build_frontend

# 启动所有服务
just up

# 停止所有服务
just down
```

### 2. 手动构建流程

#### 构建后端镜像
```bash
cd policy_chatbot_backend
docker build -f ../backend.Dockerfile -t localhost/backend .
```

#### 构建前端镜像
```bash
cd policy_chatbot_frontend

# 安装依赖并构建
pnpm install
NEXT_PUBLIC_BASE_URL=/api/v1 NODE_ENV=production pnpm build

# 构建 Docker 镜像
cd ..
docker build -f frontend.Dockerfile -t localhost/frontend ./policy_chatbot_frontend/
```

#### 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 数据库初始化

系统首次启动时会自动执行以下初始化操作：

1. **创建 pgvector 扩展**
2. **执行数据库迁移**
3. **创建默认管理员账户**
4. **初始化系统配置**

## 服务管理

### 容器管理命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend

# 进入容器
docker-compose exec backend bash

# 更新服务
docker-compose pull
docker-compose up -d
```

### 数据备份与恢复

#### 数据库备份
```bash
# 备份数据库
docker-compose exec database pg_dump -U root policy > backup.sql

# 备份数据目录
tar -czf database_backup.tar.gz deploy/data/
```

#### 数据库恢复
```bash
# 恢复数据库
docker-compose exec -T database psql -U root policy < backup.sql

# 恢复数据目录
tar -xzf database_backup.tar.gz
```

### 日志管理

#### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend
docker-compose logs database
docker-compose logs gateway

# 实时跟踪日志
docker-compose logs -f --tail=100
```

#### 日志轮转
```bash
# 配置 Docker 日志轮转
# 在 docker-compose.yaml 中添加：
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 监控与维护

### 健康检查

#### 服务健康状态
```bash
# 检查服务状态
curl http://localhost/api/v1/health

# 检查前端状态
curl http://localhost/

# 检查数据库连接
docker-compose exec backend python -c "from core.database import db; print('DB OK' if not db.is_closed() else 'DB Error')"
```

#### 系统资源监控
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h
du -sh deploy/data/

# 查看内存使用
free -h
```

### 性能优化

#### 数据库优化
```sql
-- 查看数据库大小
SELECT pg_size_pretty(pg_database_size('policy'));

-- 查看表大小
SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size 
FROM pg_tables 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 重建索引
REINDEX DATABASE policy;

-- 清理无用数据
VACUUM ANALYZE;
```

#### 缓存清理
```bash
# 清理 Docker 缓存
docker system prune -f

# 清理未使用的镜像
docker image prune -f

# 清理未使用的卷
docker volume prune -f
```

## 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :3000
netstat -tlnp | grep :8000
netstat -tlnp | grep :5432

# 检查 Docker 服务
systemctl status docker

# 检查镜像是否存在
docker images | grep localhost
```

#### 2. 数据库连接问题
```bash
# 检查数据库容器状态
docker-compose logs database

# 测试数据库连接
docker-compose exec database psql -U root -d policy -c "SELECT version();"

# 检查数据库配置
docker-compose exec backend env | grep DB_
```

#### 3. API 调用失败
```bash
# 检查后端日志
docker-compose logs backend

# 检查环境变量
docker-compose exec backend env | grep LLM_

# 测试 API 连接
curl -X POST "http://localhost/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=your_password"
```

#### 4. 前端访问问题
```bash
# 检查前端日志
docker-compose logs frontend

# 检查 Nginx 配置
docker-compose exec gateway nginx -t

# 重新加载 Nginx 配置
docker-compose exec gateway nginx -s reload
```

### 应急处理

#### 服务重启
```bash
# 快速重启所有服务
docker-compose restart

# 强制重新创建容器
docker-compose down
docker-compose up -d --force-recreate
```

#### 数据恢复
```bash
# 从备份恢复数据库
docker-compose down
rm -rf deploy/data/*
tar -xzf database_backup.tar.gz
docker-compose up -d
```

## 安全配置

### 网络安全
- **防火墙配置**: 只开放必要的端口（80, 443）
- **SSL/TLS**: 生产环境建议配置 HTTPS
- **访问控制**: 配置 IP 白名单或 VPN 访问

### 数据安全
- **密码强度**: 使用强密码和定期更换
- **数据加密**: 敏感数据的加密存储
- **备份策略**: 定期备份和异地存储

### 应用安全
- **API 密钥**: 妥善保管 AI 模型 API 密钥
- **认证令牌**: 定期更换 JWT 密钥
- **日志审计**: 启用详细的操作日志

## 总结

知识库问答助手的部署方案采用了现代化的容器编排技术，通过 Docker Compose 实现了服务的统一管理和部署。系统具有良好的可扩展性、可维护性和可靠性，支持快速部署、监控和故障恢复。通过合理的配置和运维实践，可以确保系统的稳定运行和高效服务。
