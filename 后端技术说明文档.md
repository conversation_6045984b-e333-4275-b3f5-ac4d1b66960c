# 知识库问答助手 - 后端技术说明文档

## 项目概述

知识库问答助手后端是基于 FastAPI 构建的高性能 Python Web 服务，专注于提供智能的知识库管理和 RAG（检索增强生成）问答服务。系统采用现代化的异步架构，集成了先进的向量数据库、大语言模型和文档处理技术，为前端提供强大的 AI 问答能力。

## 技术架构

### 核心技术栈

#### Web 框架与服务器
- **FastAPI 0.115.8**: 现代化的异步 Web 框架，支持自动 API 文档生成
- **Uvicorn 0.34.0**: 高性能的 ASGI 服务器
- **Python 3.13**: 最新版本的 Python，提供更好的性能和类型支持
- **Pydantic**: 数据验证和序列化库，与 FastAPI 深度集成

#### 数据库与存储
- **PostgreSQL 17**: 主数据库，存储用户、文档、对话等结构化数据
- **pgvector**: PostgreSQL 的向量扩展，支持高效的向量相似度搜索
- **Peewee 3.17.9**: 轻量级的 ORM，提供简洁的数据库操作接口
- **psycopg2-binary**: PostgreSQL 数据库适配器

#### AI 与机器学习
- **LangChain 0.3.19**: 大语言模型应用开发框架
- **OpenAI 1.63.2**: OpenAI API 客户端
- **LangChain-DeepSeek**: DeepSeek 模型集成
- **Tiktoken 0.9.0**: OpenAI 的分词器库

#### 文档处理与 RAG
- **Docling 2.28.0**: 高级文档解析库，支持多种格式
- **Chonkie 0.5.0**: 文档分块处理库
- **LangChain Text Splitters**: 文本分割工具

#### 认证与安全
- **python-jose[cryptography]**: JWT 令牌处理
- **pwdlib[bcrypt]**: 密码哈希和验证
- **python-multipart**: 多部分表单数据处理

### 项目结构

```
policy_chatbot_backend/
├── app.py                        # 应用入口点
├── settings.py                   # 配置管理
├── core/                         # 核心模块
│   ├── database.py              # 数据库连接和管理
│   ├── models.py                # 基础数据模型
│   ├── register.py              # 应用注册和中间件
│   ├── repository.py            # 数据访问层基类
│   ├── rag/                     # RAG 相关模块
│   │   ├── embedding.py         # 向量嵌入服务
│   │   ├── chunker.py           # 文档分块处理
│   │   └── rerank.py            # 重排序服务
│   └── agents/                  # AI 智能体
├── apps/                        # 应用模块
│   ├── auth/                    # 认证模块
│   ├── chat/                    # 聊天模块
│   ├── admin/                   # 管理模块
│   ├── user/                    # 用户模块
│   └── shared/                  # 共享模块
│       ├── model/               # 数据模型
│       ├── repository/          # 数据访问层
│       └── document_retriever.py # 文档检索服务
└── test/                        # 测试模块
```

## 核心功能模块

### 1. RAG 检索增强生成系统

#### 向量嵌入服务 (`core/rag/embedding.py`)
- **多模型支持**: 支持 Jina Embeddings 等多种嵌入模型
- **任务优化**: 针对检索和查询任务的不同优化策略
- **批量处理**: 高效的批量文本嵌入处理
- **异步操作**: 完全异步的嵌入计算，提升并发性能

#### 文档分块处理 (`core/rag/chunker.py`)
- **智能分块**: 基于 Markdown 结构的智能文档分块
- **层次化处理**: 支持章节、段落、句子等多层次分块
- **元数据保留**: 保留文档结构信息和上下文关系
- **长度控制**: 智能控制分块长度，优化检索效果

#### 文档检索器 (`apps/shared/document_retriever.py`)
- **多策略检索**: 支持普通检索、QA 检索等多种检索策略
- **查询优化**: 自动的查询重写和扩展
- **子查询分解**: 复杂查询的自动分解和并行处理
- **重排序**: 基于语义相似度的结果重排序

#### 检索流程
1. **查询优化**: 根据上下文优化用户查询
2. **子查询分解**: 将复杂查询分解为多个子查询
3. **并行检索**: 对子查询进行并行向量检索
4. **结果融合**: 合并和去重检索结果
5. **重排序**: 基于相关性对结果进行重排序
6. **上下文构建**: 构建最终的上下文信息

### 2. 知识库管理系统

#### 文档管理 (`apps/admin/document_manager.py`)
- **文档存储**: 支持多种格式文档的存储和管理
- **版本控制**: 文档版本管理和历史记录
- **权限控制**: 基于用户角色的文档访问权限
- **批量操作**: 支持文档的批量导入和处理

#### 文档解析 (`core/document_parser.py`)
- **多格式支持**: 支持 PDF、Word、Markdown、HTML 等格式
- **结构提取**: 自动提取文档结构和层次信息
- **内容清洗**: 智能的内容清洗和格式化
- **元数据提取**: 自动提取文档标题、作者、创建时间等信息

#### 分块管理 (`apps/shared/model/policy.py`)
- **分块存储**: 高效的文档分块存储和索引
- **向量索引**: 基于 pgvector 的高性能向量索引
- **关系维护**: 分块与原文档的关系维护
- **QA 标记**: 支持问答对的特殊标记和处理

### 3. 智能对话系统

#### 聊天管理 (`apps/chat/views.py`)
- **多线程对话**: 支持多个独立的对话线程
- **消息历史**: 完整的对话历史记录和管理
- **流式响应**: 支持 AI 回复的流式输出
- **工具调用**: 支持 AI 调用外部工具和函数

#### AI 智能体 (`core/agents/`)
- **推理智能体**: 支持 DeepSeek Reasoner 等推理模型
- **工具集成**: 集成文档检索、计算等多种工具
- **上下文管理**: 智能的对话上下文管理
- **错误处理**: 完善的错误处理和恢复机制

#### 对话数据模型 (`apps/shared/model/chat.py`)
- **线程模型**: 支持多线程对话的数据模型
- **消息模型**: 灵活的消息存储和检索
- **关系维护**: 消息间的父子关系维护
- **附加数据**: 支持消息的附加元数据存储

### 4. 用户认证与权限

#### 认证系统 (`apps/auth/auth.py`)
- **JWT 认证**: 基于 JWT 的无状态认证
- **令牌管理**: 访问令牌的生成、验证和刷新
- **权限控制**: 基于角色的访问控制（RBAC）
- **安全配置**: 完善的安全配置和密钥管理

#### 用户管理 (`apps/auth/user_manager.py`)
- **用户注册**: 支持用户注册和账户激活
- **密码安全**: 基于 bcrypt 的密码哈希和验证
- **用户信息**: 完整的用户信息管理
- **操作日志**: 用户操作的审计日志

#### 权限模型 (`apps/shared/model/user.py`)
- **角色系统**: 灵活的用户角色定义
- **权限分配**: 细粒度的权限分配和管理
- **文档权限**: 基于角色的文档访问控制
- **API 权限**: API 接口的权限控制

### 5. 数据访问层

#### 数据库管理 (`core/database.py`)
- **连接池**: 高效的数据库连接池管理
- **事务支持**: 完整的数据库事务支持
- **异步操作**: 异步的数据库操作接口
- **扩展支持**: pgvector 等扩展的自动配置

#### 仓储模式 (`core/repository.py`)
- **基础仓储**: 通用的 CRUD 操作基类
- **专用仓储**: 针对特定业务的专用仓储类
- **查询优化**: 优化的数据库查询和索引
- **缓存策略**: 智能的数据缓存策略

#### 数据迁移 (`core/migrate/`)
- **版本管理**: 数据库版本的自动管理
- **增量迁移**: 支持增量的数据库结构更新
- **数据初始化**: 系统初始化数据的自动创建
- **回滚支持**: 支持数据库迁移的回滚操作

## 设计模式与架构特点

### 1. 微服务架构
- **模块化设计**: 按功能模块划分的清晰架构
- **松耦合**: 模块间的松耦合设计，便于维护和扩展
- **依赖注入**: 基于 FastAPI 的依赖注入系统
- **接口分离**: 清晰的接口定义和实现分离

### 2. 异步编程模式
- **全异步**: 从数据库到 AI 调用的全异步架构
- **并发处理**: 高效的并发请求处理
- **资源管理**: 智能的异步资源管理和清理
- **错误处理**: 完善的异步错误处理机制

### 3. 领域驱动设计
- **领域模型**: 清晰的业务领域模型定义
- **聚合根**: 合理的聚合根设计和边界划分
- **值对象**: 恰当的值对象使用
- **领域服务**: 业务逻辑的合理封装

### 4. 配置管理
- **环境配置**: 基于 Pydantic Settings 的配置管理
- **分层配置**: 支持通用配置和场景专用配置
- **类型安全**: 完整的配置类型检查和验证
- **动态配置**: 支持运行时的配置更新

## API 设计

### RESTful API
- **标准化**: 遵循 RESTful API 设计规范
- **版本控制**: 基于路径的 API 版本控制
- **文档生成**: 基于 OpenAPI 的自动文档生成
- **错误处理**: 统一的错误响应格式

### 接口分组
- `/api/v1/chat`: 聊天相关接口
- `/api/v1/admin`: 管理员接口
- `/api/v1/auth`: 认证相关接口
- `/api/v1/user`: 用户相关接口

### 数据格式
- **JSON**: 统一使用 JSON 数据格式
- **类型验证**: 基于 Pydantic 的请求/响应验证
- **错误码**: 标准化的错误码和错误信息
- **分页**: 统一的分页响应格式

## 性能优化

### 数据库优化
- **索引策略**: 合理的数据库索引设计
- **查询优化**: SQL 查询的性能优化
- **连接池**: 高效的数据库连接池配置
- **向量索引**: pgvector 的向量索引优化

### 缓存策略
- **查询缓存**: 频繁查询的结果缓存
- **向量缓存**: 嵌入向量的智能缓存
- **会话缓存**: 用户会话数据的缓存
- **配置缓存**: 系统配置的内存缓存

### 并发处理
- **异步 I/O**: 全面的异步 I/O 操作
- **线程池**: 合理的线程池配置
- **资源限制**: 智能的资源使用限制
- **负载均衡**: 支持水平扩展的负载均衡

## 环境配置

### 必需配置
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=policy
DB_USERNAME=root
DB_PASSWORD=your_password

# LLM 配置
LLM_KEY=your_api_key
LLM_MODEL=deepseek-v3
LLM_API_BASE_URL=https://api.deepseek.com

# 嵌入模型配置
EMBEDDING_URL=https://api.jina.ai/v1/embeddings
EMBEDDING_KEY=your_embedding_key
EMBEDDING_MODEL=jina-embeddings-v3

# 重排序配置
RERANK_MODEL=jina-reranker-v2-base-multilingual
RERANK_URL=https://api.jina.ai/v1/rerank
```

### 可选配置
```bash
# 调试模式
DEBUG_MODE=false

# 日志配置
LOG_TO_FILE=false

# RAG 参数
RAG_RETRIEVE_TOP_K=5
RAG_RETRIEVE_SCALE_FACTOR=1.0
```

## 部署与运维

### Docker 部署
- **多阶段构建**: 优化的 Docker 镜像构建
- **UV 包管理**: 使用 UV 进行快速依赖安装
- **健康检查**: 完善的容器健康检查
- **日志管理**: 结构化的日志输出和管理

### 监控与日志
- **结构化日志**: 基于 Python logging 的结构化日志
- **性能监控**: API 响应时间和资源使用监控
- **错误追踪**: 完善的错误追踪和报告
- **业务指标**: 关键业务指标的监控和统计

## 总结

知识库问答助手后端采用现代化的 Python 技术栈，构建了高性能、可扩展的 RAG 问答系统。通过异步架构、向量数据库和先进的 AI 技术，为用户提供了智能、准确的知识库问答服务。系统在架构设计、性能优化和运维管理方面都体现了最佳实践，确保了系统的稳定性和可维护性。
