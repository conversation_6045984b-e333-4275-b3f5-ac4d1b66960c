set dotenv-load := true
set dotenv-path := ".env"

sync_backend:
  cd policy_chatbot_backend && git pull

sync_frontend:
  cd policy_chatbot_frontend && git pull

sync: sync_backend sync_frontend

build_backend:
  sd 'https://files.pythonhosted.org/|https://pypi.org/' 'https://linux.xidian.edu.cn/mirrors/pypi/' policy_chatbot_backend/uv.lock && \
  docker build -f backend.Dockerfile -t localhost/backend policy_chatbot_backend/ --network host && \
  cd policy_chatbot_backend && git restore uv.lock

build_frontend:
  cd policy_chatbot_frontend && \
  pnpm build && \
  bun i && NEXT_PUBLIC_BASE_URL=/api/v1 NODE_ENV=production bun run build && \
  cd ../ && \
  docker build -f frontend.Dockerfile -t localhost/frontend ./policy_chatbot_frontend/

build: build_backend build_frontend

commit:
  git commit -a

all: build commit
